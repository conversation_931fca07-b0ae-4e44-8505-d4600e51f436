<?php
/**
 * 主题选项配置文件
 *
 * 使用Xun Framework创建主题选项页面。
 *
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

// 防止直接访问
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * 创建主题选项页面
 *
 * 使用Xun Framework创建主题选项页面的配置。
 *
 * @since 1.0
 */
function xun_create_theme_options() {

    // 检查框架是否可用
    if ( ! class_exists( 'XUN' ) ) {
        return;
    }

    // 创建选项页面
    XUN::createOptions( 'xun_test_options', array(
        'menu_title'      => 'Xun 主题选项',
        'menu_slug'       => 'xun-theme-options',
        'menu_type'       => 'menu',
        'menu_capability' => 'manage_options',
        'menu_icon'       => 'dashicons-admin-generic',
        'menu_position'   => 59,
        'framework_title' => 'Xun Framework 测试主题 <small>v1.0</small>',
        'theme'           => 'dark',
        'ajax_save'       => true,
        'show_reset_all'  => true,
        // 自定义欢迎页面内容（可选）
        'welcome_content' => '
            <!-- 欢迎页面头部 -->
            <div class="bg-gradient-to-r from-purple-500 to-pink-500 px-8 py-12 text-white text-center rounded-t-lg">
                <div class="max-w-2xl mx-auto">
                    <div class="mb-6">
                        <span class="inline-block p-4 bg-white bg-opacity-20 rounded-full">
                            <span class="dashicons dashicons-admin-customizer text-4xl"></span>
                        </span>
                    </div>
                    <h1 class="text-3xl font-bold mb-4">欢迎使用 Kuang 主题</h1>
                    <p class="text-purple-100 text-lg">现代化的WordPress主题，让您的网站更加出色</p>
                </div>
            </div>

            <!-- 欢迎页面内容 -->
            <div class="p-8">
                <div class="grid md:grid-cols-2 gap-8 mb-8">
                    <div>
                        <h3 class="text-xl font-semibold mb-4 text-gray-900 flex items-center">
                            <span class="dashicons dashicons-admin-appearance mr-2 text-purple-500"></span>
                            主题特色
                        </h3>
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-center">
                                <span class="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                                响应式设计，完美适配各种设备
                            </li>
                            <li class="flex items-center">
                                <span class="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                                SEO优化，提升搜索引擎排名
                            </li>
                            <li class="flex items-center">
                                <span class="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                                高性能优化，快速加载
                            </li>
                            <li class="flex items-center">
                                <span class="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                                丰富的自定义选项
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-4 text-gray-900 flex items-center">
                            <span class="dashicons dashicons-admin-tools mr-2 text-pink-500"></span>
                            快速配置
                        </h3>
                        <div class="space-y-3">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="font-medium text-gray-900 mb-1">网站信息</h4>
                                <p class="text-sm text-gray-600">配置网站基本信息、SEO设置和社交媒体</p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="font-medium text-gray-900 mb-1">高级设置</h4>
                                <p class="text-sm text-gray-600">自定义主题高级功能和性能选项</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速开始区域 -->
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 text-center">
                    <h3 class="text-lg font-semibold mb-2 text-gray-900">开始配置您的主题</h3>
                    <p class="text-gray-600 mb-4">点击左侧菜单开始个性化您的网站设置</p>
                    <div class="text-sm text-gray-500">
                        <p>Kuang Theme v1.0 | 基于 Xun Framework 构建</p>
                    </div>
                </div>

                <!-- 版本信息 -->
                <div class="mt-8 pt-6 border-t border-gray-200 text-center text-gray-500 text-sm">
                    <p>Kuang Theme v1.0 | 基于 Xun Framework 构建</p>
                </div>
            </div>
        ',
        // 自定义版权信息（可选）
        'copyright' => '© 2025 Kuang Theme - 基于 XUN Framework v1.0 构建',
        // 自定义版权链接URL（可选）
        'copyright_url' => 'https://kuangtheme.com',
        // 自定义版本号（可选）
        'version' => 'v2.1.0',
    ) );

    // 创建背景设置父菜单（测试background字段）
    XUN::createSection( 'xun_test_options', array(
        'id'     => 'background_settings',
        'title'  => '背景设置',
        'desc'   => '网站背景样式设置',
        'icon'   => 'dashicons-format-image',
        'fields' => array(), // 父菜单不包含字段
    ) );

    // 创建背景设置子菜单
    XUN::createSection( 'xun_test_options', array(
        'title'  => '网站背景',
        'desc'   => '设置网站的背景颜色和背景图片',
        'icon'   => 'dashicons-admin-appearance',
        'parent' => 'background_settings',
        'fields' => array(

            array(
                'id'    => 'site_background',
                'type'  => 'background',
                'title' => '网站主背景',
                'desc'  => '设置网站的整体背景样式，包括颜色和图片',
                'default' => array(
                    'background-color' => '#ffffff',
                ),
            ),

            array(
                'id'    => 'header_background',
                'type'  => 'background',
                'title' => '头部背景',
                'desc'  => '设置网站头部的背景样式',
                'background_attachment' => true, // 启用背景附着选项
                'default' => array(
                    'background-color' => '#1f2937',
                ),
            ),

            array(
                'id'    => 'footer_background',
                'type'  => 'background',
                'title' => '底部背景',
                'desc'  => '设置网站底部的背景样式',
                'background_attachment' => false, // 禁用背景附着选项
                'default' => array(
                    'background-color' => '#374151',
                ),
            ),

        ),
    ) );

    // 创建网站信息父菜单（只作为容器，不显示内容）
    XUN::createSection( 'xun_test_options', array(
        'id'     => 'site_basic',
        'title'  => '网站信息',
        'desc'   => '网站基本信息和设置管理',
        'icon'   => 'dashicons-admin-home',
        'fields' => array(), // 父菜单不包含字段
    ) );

    // 创建基本信息子菜单
    XUN::createSection( 'xun_test_options', array(
        'title'  => '基本信息',
        'desc'   => '配置网站的基本信息和联系方式',
        'icon'   => 'dashicons-admin-site-alt3',
        'parent' => 'site_basic',
        'fields' => array(

            // 网站标题
            array(
                'id'          => 'site_title',
                'type'        => 'text',
                'title'       => '网站标题',
                'desc'        => '自定义网站标题，留空则使用WordPress默认设置',
                'placeholder' => '请输入网站标题...',
                'default'     => '',
            ),

            // 网站描述
            array(
                'id'          => 'site_description',
                'type'        => 'text',
                'title'       => '网站描述',
                'desc'        => '网站的简短描述',
                'placeholder' => '请输入网站描述...',
                'default'     => '',
            ),

            // 联系邮箱
            array(
                'id'          => 'contact_email',
                'type'        => 'text',
                'title'       => '联系邮箱',
                'desc'        => '网站的联系邮箱地址',
                'placeholder' => '<EMAIL>',
                'attributes'  => array(
                    'type' => 'email',
                ),
                'default'     => '',
            ),

            // 网站URL
            array(
                'id'          => 'site_url',
                'type'        => 'text',
                'title'       => '网站URL',
                'desc'        => '网站的主要URL地址',
                'placeholder' => 'https://example.com',
                'attributes'  => array(
                    'type' => 'url',
                ),
                'default'     => '',
            ),

            // 电话号码
            array(
                'id'          => 'phone_number',
                'type'        => 'text',
                'title'       => '联系电话',
                'desc'        => '网站的联系电话号码',
                'placeholder' => '+86 138 0000 0000',
                'attributes'  => array(
                    'type' => 'tel',
                ),
                'default'     => '',
            ),

        ),
    ) );

    // 创建SEO设置子菜单
    XUN::createSection( 'xun_test_options', array(
        'title'  => 'SEO设置',
        'desc'   => '搜索引擎优化相关配置',
        'icon'   => 'dashicons-search',
        'parent' => 'site_basic',
        'fields' => array(

            // Meta关键词
            array(
                'id'          => 'meta_keywords',
                'type'        => 'text',
                'title'       => 'Meta关键词',
                'desc'        => '网站的Meta关键词，用逗号分隔',
                'placeholder' => '关键词1, 关键词2, 关键词3',
                'default'     => '',
            ),

            // Meta描述
            array(
                'id'          => 'meta_description',
                'type'        => 'text',
                'title'       => 'Meta描述',
                'desc'        => '网站的Meta描述标签内容',
                'placeholder' => '请输入Meta描述...',
                'default'     => '',
            ),

        ),
    ) );

    // 创建高级设置区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => '高级设置',
        'desc'   => '配置主题的高级功能和性能选项',
        'icon'   => 'dashicons-admin-settings',
        'fields' => array(

            // API密钥
            array(
                'id'          => 'api_key',
                'type'        => 'text',
                'title'       => 'API密钥',
                'desc'        => '第三方服务的API密钥',
                'placeholder' => '请输入API密钥...',
                'attributes'  => array(
                    'type' => 'password',
                ),
                'default'     => '',
            ),

            // 最大文章数量
            array(
                'id'          => 'max_posts',
                'type'        => 'text',
                'title'       => '最大文章数量',
                'desc'        => '首页显示的最大文章数量',
                'placeholder' => '10',
                'attributes'  => array(
                    'type' => 'number',
                    'min'  => '1',
                    'max'  => '100',
                ),
                'default'     => '10',
            ),

            // 自定义代码
            array(
                'id'          => 'custom_css',
                'type'        => 'textarea',
                'title'       => '自定义CSS',
                'desc'        => '添加自定义CSS代码，支持多行输入',
                'placeholder' => '/* 在这里添加自定义CSS代码 */',
                'rows'        => 6,
                'default'     => '',
            ),

            // Textarea测试 - 可拖拽调整大小
            array(
                'id'          => 'textarea_resizable',
                'type'        => 'textarea',
                'title'       => 'Textarea测试 - 可拖拽调整',
                'desc'        => '这个textarea可以通过拖拽右下角来调整大小，支持字符计数',
                'placeholder' => '请输入内容，可以拖拽右下角调整大小...',
                'rows'        => 4,
                'resizable'   => true,
                'show_count'  => true,
                'maxlength'   => 500,
                'default'     => '',
            ),

            // Textarea测试 - 自动调整高度
            array(
                'id'          => 'textarea_auto_resize',
                'type'        => 'textarea',
                'title'       => 'Textarea测试 - 自动调整高度',
                'desc'        => '这个textarea会根据内容自动调整高度，不能手动拖拽',
                'placeholder' => '请输入内容，高度会自动调整...',
                'rows'        => 3,
                'auto_resize' => true,
                'show_count'  => true,
                'maxlength'   => 1000,
                'default'     => '',
            ),

            // Textarea测试 - 禁用调整大小
            array(
                'id'          => 'textarea_no_resize',
                'type'        => 'textarea',
                'title'       => 'Textarea测试 - 禁用调整大小',
                'desc'        => '这个textarea禁用了大小调整功能',
                'placeholder' => '固定大小的textarea...',
                'rows'        => 5,
                'resizable'   => false,
                'show_count'  => true,
                'maxlength'   => 300,
                'default'     => '',
            ),

        ),
    ) );

    // 创建社交媒体区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => '社交媒体',
        'desc'   => '配置社交媒体链接和账号信息',
        'icon'   => 'dashicons-share',
        'fields' => array(

            // 微博链接
            array(
                'id'          => 'weibo_url',
                'type'        => 'text',
                'title'       => '微博链接',
                'desc'        => '您的微博主页链接',
                'placeholder' => 'https://weibo.com/username',
                'attributes'  => array(
                    'type' => 'url',
                ),
                'default'     => '',
            ),

            // 微信号
            array(
                'id'          => 'wechat_id',
                'type'        => 'text',
                'title'       => '微信号',
                'desc'        => '您的微信号或微信公众号',
                'placeholder' => '请输入微信号...',
                'default'     => '',
            ),

            // QQ号
            array(
                'id'          => 'qq_number',
                'type'        => 'text',
                'title'       => 'QQ号',
                'desc'        => '您的QQ号码',
                'placeholder' => '123456789',
                'attributes'  => array(
                    'type' => 'number',
                ),
                'default'     => '',
            ),

            // GitHub链接
            array(
                'id'          => 'github_url',
                'type'        => 'text',
                'title'       => 'GitHub链接',
                'desc'        => '您的GitHub主页链接',
                'placeholder' => 'https://github.com/username',
                'attributes'  => array(
                    'type' => 'url',
                ),
                'default'     => '',
            ),

        ),
    ) );

    // 创建媒体字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => '媒体字段测试',
        'desc'   => '媒体字段功能，包括图片、视频、音频等多种媒体类型',
        'icon'   => 'dashicons-format-image',
        'fields' => array(

            // 基础媒体字段
            array(
                'id'    => 'basic_media',
                'type'  => 'media',
                'title' => '基础媒体字段',
                'desc'  => '最基本的媒体选择字段，支持所有媒体类型',
            ),

            // 仅图片媒体字段
            array(
                'id'      => 'image_only',
                'type'    => 'media',
                'title'   => '仅图片字段',
                'desc'    => '限制只能选择图片文件的媒体字段',
                'library' => array( 'image' ),
                'preview_size' => 'medium',
            ),

            // 网站Logo
            array(
                'id'             => 'site_logo',
                'type'           => 'media',
                'title'          => '网站Logo',
                'desc'           => '上传您的网站Logo图片，建议尺寸：200x60像素',
                'library'        => array( 'image' ),
                'preview'        => true,
                'preview_width'  => 200,
                'preview_height' => 60,
                'button_title'   => '选择Logo',
                'remove_title'   => '移除Logo',
                'placeholder'    => '未设置Logo',
            ),

            // 网站图标
            array(
                'id'             => 'site_favicon',
                'type'           => 'media',
                'title'          => '网站图标 (Favicon)',
                'desc'           => '上传网站图标，建议尺寸：32x32像素，格式：ICO、PNG',
                'library'        => array( 'image' ),
                'preview'        => true,
                'preview_width'  => 32,
                'preview_height' => 32,
                'compact_mode'   => true,
            ),

            // 背景图片
            array(
                'id'             => 'hero_background',
                'type'           => 'media',
                'title'          => '首页背景图',
                'desc'           => '设置首页的背景图片，建议尺寸：1920x1080像素',
                'library'        => array( 'image' ),
                'preview'        => true,
                'preview_size'   => 'large',
                'show_filename'  => true,
                'show_filesize'  => true,
                'show_dimensions' => true,
            ),

            // 视频字段
            array(
                'id'      => 'intro_video',
                'type'    => 'media',
                'title'   => '介绍视频',
                'desc'    => '上传网站介绍视频，支持MP4、AVI、MOV等格式',
                'library' => array( 'video' ),
                'preview' => true,
            ),

            // 音频字段
            array(
                'id'      => 'background_music',
                'type'    => 'media',
                'title'   => '背景音乐',
                'desc'    => '上传背景音乐文件，支持MP3、WAV等格式',
                'library' => array( 'audio' ),
                'preview' => true,
            ),

            // 文档字段
            array(
                'id'      => 'user_manual',
                'type'    => 'media',
                'title'   => '用户手册',
                'desc'    => '上传用户手册PDF文档',
                'library' => array( 'application' ),
                'preview' => false,
                'url'     => true,
            ),

            // 多媒体字段（支持多选）
            array(
                'id'       => 'gallery_images',
                'type'     => 'media',
                'title'    => '图片画廊',
                'desc'     => '选择多张图片创建画廊（注意：当前版本暂不支持多选，此为演示配置）',
                'library'  => array( 'image' ),
                'multiple' => false, // 当前版本设为false
                'preview'  => true,
                'preview_size' => 'thumbnail',
            ),

            // 自定义预览尺寸
            array(
                'id'             => 'custom_preview',
                'type'           => 'media',
                'title'          => '自定义预览尺寸',
                'desc'           => '演示自定义预览尺寸的媒体字段',
                'library'        => array( 'image' ),
                'preview'        => true,
                'preview_width'  => 300,
                'preview_height' => 200,
                'show_filename'  => true,
                'show_filesize'  => false,
                'show_dimensions' => true,
            ),

            // 紧凑模式
            array(
                'id'           => 'compact_media',
                'type'         => 'media',
                'title'        => '紧凑模式媒体字段',
                'desc'         => '演示紧凑模式的媒体字段，适合空间有限的场景',
                'compact_mode' => true,
                'preview'      => false,
                'url'          => true,
            ),

            // 无预览模式
            array(
                'id'      => 'no_preview_media',
                'type'    => 'media',
                'title'   => '无预览模式',
                'desc'    => '不显示预览的媒体字段，仅显示文件信息',
                'preview' => false,
                'url'     => true,
                'show_filename'  => true,
                'show_filesize'  => true,
                'show_dimensions' => true,
            ),

            // 自定义按钮文字
            array(
                'id'           => 'custom_buttons',
                'type'         => 'media',
                'title'        => '自定义按钮文字',
                'desc'         => '演示自定义按钮文字的媒体字段',
                'library'      => array( 'image' ),
                'button_title' => '选择产品图片',
                'remove_title' => '删除图片',
                'placeholder'  => '请选择产品图片...',
            ),

        ),
    ) );

    // 创建选择字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => '选择字段测试',
        'desc'   => '选择字段功能，包括单选、多选、搜索等功能',
        'icon'   => 'dashicons-list-view',
        'fields' => array(

            // 基础单选字段
            array(
                'id'      => 'basic_select',
                'type'    => 'select',
                'title'   => '基础选择字段',
                'desc'    => '最基本的单选下拉列表',
                'options' => array(
                    'option1' => '选项一',
                    'option2' => '选项二',
                    'option3' => '选项三',
                    'option4' => '选项四',
                ),
                'default' => 'option1',
            ),

            // 多选字段
            array(
                'id'       => 'multiple_select',
                'type'     => 'select',
                'title'    => '多选字段',
                'desc'     => '支持选择多个选项的下拉列表',
                'multiple' => true,
                'options'  => array(
                    'red'    => '红色',
                    'green'  => '绿色',
                    'blue'   => '蓝色',
                    'yellow' => '黄色',
                    'purple' => '紫色',
                    'orange' => '橙色',
                ),
                'select_all' => true,
                'default'    => array( 'red', 'blue' ),
            ),

            // 可搜索字段
            array(
                'id'         => 'searchable_select',
                'type'       => 'select',
                'title'      => '可搜索选择字段',
                'desc'       => '支持搜索过滤的下拉列表',
                'searchable' => true,
                'options'    => array(
                    'beijing'   => '北京',
                    'shanghai'  => '上海',
                    'guangzhou' => '广州',
                    'shenzhen'  => '深圳',
                    'hangzhou'  => '杭州',
                    'nanjing'   => '南京',
                    'wuhan'     => '武汉',
                    'chengdu'   => '成都',
                    'xian'      => '西安',
                    'chongqing' => '重庆',
                ),
                'placeholder' => '选择城市...',
            ),

            // 分组选项字段
            array(
                'id'      => 'grouped_select',
                'type'    => 'select',
                'title'   => '分组选择字段',
                'desc'    => '支持选项分组的下拉列表',
                'options' => array(
                    '前端技术' => array(
                        'html'       => 'HTML',
                        'css'        => 'CSS',
                        'javascript' => 'JavaScript',
                        'vue'        => 'Vue.js',
                        'react'      => 'React',
                    ),
                    '后端技术' => array(
                        'php'    => 'PHP',
                        'python' => 'Python',
                        'nodejs' => 'Node.js',
                        'java'   => 'Java',
                        'golang' => 'Go',
                    ),
                    '数据库' => array(
                        'mysql'      => 'MySQL',
                        'postgresql' => 'PostgreSQL',
                        'mongodb'    => 'MongoDB',
                        'redis'      => 'Redis',
                    ),
                ),
                'searchable' => true,
                'placeholder' => '选择技术栈...',
            ),

            // 多选分组字段
            array(
                'id'       => 'multiple_grouped_select',
                'type'     => 'select',
                'title'    => '多选分组字段',
                'desc'     => '支持多选和分组的下拉列表',
                'multiple' => true,
                'options'  => array(
                    '水果' => array(
                        'apple'  => '苹果',
                        'banana' => '香蕉',
                        'orange' => '橙子',
                        'grape'  => '葡萄',
                    ),
                    '蔬菜' => array(
                        'tomato'  => '番茄',
                        'carrot'  => '胡萝卜',
                        'cabbage' => '白菜',
                        'potato'  => '土豆',
                    ),
                    '肉类' => array(
                        'beef'    => '牛肉',
                        'pork'    => '猪肉',
                        'chicken' => '鸡肉',
                        'fish'    => '鱼肉',
                    ),
                ),
                'searchable'  => true,
                'select_all'  => true,
                'placeholder' => '选择食物类型...',
            ),

            // 不可清空字段
            array(
                'id'        => 'non_clearable_select',
                'type'      => 'select',
                'title'     => '不可清空字段',
                'desc'      => '不显示清空按钮的选择字段',
                'clearable' => false,
                'options'   => array(
                    'small'  => '小号',
                    'medium' => '中号',
                    'large'  => '大号',
                    'xlarge' => '超大号',
                ),
                'default' => 'medium',
            ),

            // 选择后不关闭字段
            array(
                'id'              => 'keep_open_select',
                'type'            => 'select',
                'title'           => '选择后保持打开',
                'desc'            => '选择选项后不自动关闭下拉列表',
                'close_on_select' => false,
                'options'         => array(
                    'feature1' => '功能一',
                    'feature2' => '功能二',
                    'feature3' => '功能三',
                    'feature4' => '功能四',
                ),
                'placeholder' => '选择功能...',
            ),

            // 自定义高度字段
            array(
                'id'         => 'custom_height_select',
                'type'       => 'select',
                'title'      => '自定义高度字段',
                'desc'       => '设置了自定义最大高度的下拉列表',
                'max_height' => 150,
                'options'    => array(
                    'item1'  => '项目一',
                    'item2'  => '项目二',
                    'item3'  => '项目三',
                    'item4'  => '项目四',
                    'item5'  => '项目五',
                    'item6'  => '项目六',
                    'item7'  => '项目七',
                    'item8'  => '项目八',
                    'item9'  => '项目九',
                    'item10' => '项目十',
                ),
                'searchable' => true,
            ),

            // 自定义文本字段
            array(
                'id'                 => 'custom_text_select',
                'type'               => 'select',
                'title'              => '自定义文本字段',
                'desc'               => '自定义各种提示文本的选择字段',
                'searchable'         => true,
                'placeholder'        => '请选择您的选项...',
                'search_placeholder' => '输入关键词搜索...',
                'no_results_text'    => '没有找到匹配的选项',
                'loading_text'       => '正在加载选项...',
                'options'            => array(
                    'option1' => '这是一个很长的选项名称用于测试文本截断效果',
                    'option2' => '短选项',
                    'option3' => '中等长度的选项名称',
                    'option4' => '另一个测试选项',
                ),
            ),

        ),
    ) );

    // 创建日期设置区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => '日期设置',
        'desc'   => '现代化日期选择器功能演示和配置',
        'icon'   => 'dashicons-calendar-alt',
        'fields' => array(

            // 基础日期选择器
            array(
                'id'       => 'basic_date',
                'type'     => 'date',
                'title'    => '基础日期选择器',
                'desc'     => '最简单的日期选择器，使用默认配置',
                'default'  => '',
            ),

            // 带默认值的日期选择器
            array(
                'id'       => 'default_date',
                'type'     => 'date',
                'title'    => '带默认值的日期选择器',
                'desc'     => '设置了默认值为今天的日期选择器',
                'default'  => date('Y-m-d'),
            ),

            // 自定义格式的日期选择器
            array(
                'id'       => 'custom_format_date',
                'type'     => 'date',
                'title'    => '自定义格式日期选择器',
                'desc'     => '使用自定义显示格式的日期选择器（显示星期）',
                'settings' => array(
                    'date_format'    => 'Y-m-d',
                    'display_format' => 'Y年m月d日 (l)',
                    'placeholder'    => '请选择一个日期（显示星期）',
                    'show_today'     => true,
                    'show_clear'     => false, // 隐藏清除按钮
                ),
                'default'  => '',
            ),

            // 带范围限制的日期选择器
            array(
                'id'       => 'limited_date',
                'type'     => 'date',
                'title'    => '限制范围的日期选择器',
                'desc'     => '只能选择2020年到2030年之间的日期',
                'settings' => array(
                    'date_format'    => 'Y-m-d',
                    'display_format' => 'Y年m月d日',
                    'placeholder'    => '请选择日期（2020-2030）',
                    'min_date'       => '2020-01-01',
                    'max_date'       => '2030-12-31',
                ),
                'default'  => '',
            ),

            // 禁用周末的日期选择器
            array(
                'id'       => 'weekday_only_date',
                'type'     => 'date',
                'title'    => '工作日日期选择器',
                'desc'     => '禁用周末（周六和周日）的日期选择器，显示星期信息',
                'settings' => array(
                    'date_format'    => 'Y-m-d',
                    'display_format' => 'Y年m月d日 (l)',
                    'placeholder'    => '请选择工作日（周末不可选）',
                    'disabled_days'  => array( 0, 6 ), // 0=周日, 6=周六
                    'show_today'     => false, // 隐藏今天按钮
                    'show_clear'     => true,
                    'auto_close'     => false, // 不自动关闭
                ),
                'default'  => '',
            ),

            // 日期范围选择器
            array(
                'id'         => 'date_range',
                'type'       => 'date',
                'title'      => '日期范围选择器',
                'desc'       => '选择一个日期范围，包含开始日期和结束日期',
                'date_range' => true,
                'text_from'  => '开始日期',
                'text_to'    => '结束日期',
                'settings'   => array(
                    'date_format'    => 'Y-m-d',
                    'display_format' => 'Y年m月d日',
                    'placeholder'    => '请选择日期',
                ),
                'default'    => array(
                    'from' => '',
                    'to'   => '',
                ),
            ),

            // 高级配置的日期选择器
            array(
                'id'       => 'advanced_date',
                'type'     => 'date',
                'title'    => '高级配置日期选择器',
                'desc'     => '展示所有高级配置选项：限制范围、禁用特定日期、周日开始',
                'settings' => array(
                    'date_format'    => 'Y-m-d',
                    'display_format' => 'Y年m月d日 (l)',
                    'placeholder'    => '高级配置测试（有多种限制）',
                    'min_date'       => date('Y-m-d', strtotime('-6 months')),
                    'max_date'       => date('Y-m-d', strtotime('+6 months')),
                    'disabled_dates' => array(
                        date('Y-m-d', strtotime('tomorrow')),
                        date('Y-m-d', strtotime('+2 days')),
                        date('Y-m-d', strtotime('+3 days')),
                    ),
                    'disabled_days'  => array( 2 ), // 禁用周二
                    'first_day'      => 0, // 周日为第一天（与其他不同）
                    'show_today'     => true,
                    'show_clear'     => true,
                    'auto_close'     => false, // 不自动关闭
                ),
                'default'  => '',
            ),

            // 日期时间选择器（24小时制）
            array(
                'id'       => 'datetime_24h',
                'type'     => 'date',
                'title'    => '日期时间选择器（24小时制）',
                'desc'     => '可以同时选择日期和时间，时间可以手动输入，支持快速预设',
                'settings' => array(
                    'date_format'    => 'Y-m-d',
                    'display_format' => 'Y年m月d日',
                    'placeholder'    => '请选择日期和时间',
                    'enable_time'    => true,
                    'time_format'    => '24',
                    'show_seconds'   => false,
                ),
                'default'  => '',
            ),

            // 日期时间选择器（12小时制）
            array(
                'id'       => 'datetime_12h',
                'type'     => 'date',
                'title'    => '日期时间选择器（12小时制）',
                'desc'     => '12小时制格式，带AM/PM选择，时间输入框支持手动输入',
                'settings' => array(
                    'date_format'    => 'Y-m-d',
                    'display_format' => 'Y年m月d日',
                    'placeholder'    => '请选择日期和时间（12小时制）',
                    'enable_time'    => true,
                    'time_format'    => '12',
                    'show_seconds'   => false,
                ),
                'default'  => '',
            ),

            // 精确日期时间选择器
            array(
                'id'       => 'datetime_precise',
                'type'     => 'date',
                'title'    => '精确日期时间选择器',
                'desc'     => '精确到秒的日期时间选择器，所有时间部分都可以手动输入',
                'settings' => array(
                    'date_format'    => 'Y-m-d',
                    'display_format' => 'Y年m月d日',
                    'placeholder'    => '请选择精确的日期时间',
                    'enable_time'    => true,
                    'time_format'    => '24',
                    'show_seconds'   => true,
                    'auto_close'     => false, // 不自动关闭，方便调整时间
                ),
                'default'  => '',
            ),

            // 日期时间选择器（24小时制）
            array(
                'id'       => 'datetime_24h',
                'type'     => 'date',
                'title'    => '日期时间选择器（24小时制）',
                'desc'     => '可以同时选择日期和时间，时间可以手动输入，支持快速预设',
                'settings' => array(
                    'date_format'    => 'Y-m-d',
                    'display_format' => 'Y年m月d日',
                    'placeholder'    => '请选择日期和时间',
                    'enable_time'    => true,
                    'time_format'    => '24',
                    'show_seconds'   => false,
                ),
                'default'  => '',
            ),

            // 日期时间选择器（12小时制）
            array(
                'id'       => 'datetime_12h',
                'type'     => 'date',
                'title'    => '日期时间选择器（12小时制）',
                'desc'     => '12小时制格式，带AM/PM选择，时间输入框支持手动输入',
                'settings' => array(
                    'date_format'    => 'Y-m-d',
                    'display_format' => 'Y年m月d日',
                    'placeholder'    => '请选择日期和时间（12小时制）',
                    'enable_time'    => true,
                    'time_format'    => '12',
                    'show_seconds'   => false,
                ),
                'default'  => '',
            ),

            // 精确日期时间选择器
            array(
                'id'       => 'datetime_precise',
                'type'     => 'date',
                'title'    => '精确日期时间选择器',
                'desc'     => '精确到秒的日期时间选择器，所有时间部分都可以手动输入',
                'settings' => array(
                    'date_format'    => 'Y-m-d',
                    'display_format' => 'Y年m月d日',
                    'placeholder'    => '请选择精确的日期时间',
                    'enable_time'    => true,
                    'time_format'    => '24',
                    'show_seconds'   => true,
                    'auto_close'     => false, // 不自动关闭，方便调整时间
                ),
                'default'  => '',
            ),

            // 日期时间选择器（24小时制）
            array(
                'id'       => 'datetime_24h',
                'type'     => 'date',
                'title'    => '日期时间选择器（24小时制）',
                'desc'     => '可以同时选择日期和时间的选择器，24小时制格式',
                'settings' => array(
                    'date_format'    => 'Y-m-d',
                    'display_format' => 'Y年m月d日',
                    'placeholder'    => '请选择日期和时间',
                    'enable_time'    => true,
                    'time_format'    => '24',
                    'show_seconds'   => false,
                    'minute_step'    => 15, // 15分钟步长
                ),
                'default'  => '',
            ),

            // 日期时间选择器（12小时制）
            array(
                'id'       => 'datetime_12h',
                'type'     => 'date',
                'title'    => '日期时间选择器（12小时制）',
                'desc'     => '可以同时选择日期和时间的选择器，12小时制格式，带AM/PM',
                'settings' => array(
                    'date_format'    => 'Y-m-d',
                    'display_format' => 'Y年m月d日',
                    'placeholder'    => '请选择日期和时间（12小时制）',
                    'enable_time'    => true,
                    'time_format'    => '12',
                    'show_seconds'   => false,
                    'minute_step'    => 5, // 5分钟步长
                ),
                'default'  => '',
            ),

            // 精确日期时间选择器
            array(
                'id'       => 'datetime_precise',
                'type'     => 'date',
                'title'    => '精确日期时间选择器',
                'desc'     => '精确到秒的日期时间选择器，适用于需要精确时间的场景',
                'settings' => array(
                    'date_format'    => 'Y-m-d',
                    'display_format' => 'Y年m月d日',
                    'placeholder'    => '请选择精确的日期时间',
                    'enable_time'    => true,
                    'time_format'    => '24',
                    'show_seconds'   => true,
                    'minute_step'    => 1,
                    'second_step'    => 1,
                    'auto_close'     => false, // 不自动关闭，方便调整时间
                ),
                'default'  => '',
            ),

        ),
    ) );

    // 创建UI展示区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => 'UI 展示',
        'desc'   => '展示新的简约扁平化UI设计效果',
        'icon'   => 'dashicons-art',
        'fields' => array(

            // 示例文本字段
            array(
                'id'          => 'ui_demo_text',
                'type'        => 'text',
                'title'       => '示例文本字段',
                'desc'        => '这是一个使用新UI设计的文本输入字段',
                'placeholder' => '请输入示例文本...',
                'default'     => 'Hello, Xun Framework!',
            ),

            // 示例邮箱字段
            array(
                'id'          => 'ui_demo_email',
                'type'        => 'text',
                'title'       => '示例邮箱字段',
                'desc'        => '展示邮箱输入字段的新样式',
                'placeholder' => '<EMAIL>',
                'attributes'  => array(
                    'type' => 'email',
                ),
                'default'     => '',
            ),

            // 示例数字字段
            array(
                'id'          => 'ui_demo_number',
                'type'        => 'text',
                'title'       => '示例数字字段',
                'desc'        => '展示数字输入字段的现代化设计',
                'placeholder' => '100',
                'attributes'  => array(
                    'type' => 'number',
                    'min'  => '0',
                    'max'  => '1000',
                ),
                'default'     => '42',
            ),

        ),
    ) );
  // 创建单选按钮字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => '单选按钮字段测试',
        'desc'   => '测试和展示现代化单选按钮字段的各种功能和配置选项',
        'icon'   => 'dashicons-yes-alt',
        'fields' => array(

            // 基础单选按钮
            array(
                'id'    => 'basic_radio',
                'type'  => 'radio',
                'title' => '基础单选按钮',
                'desc'  => '最简单的单选按钮组，适用于基础选择场景',
                'options' => array(
                    'option1' => '选项一',
                    'option2' => '选项二',
                    'option3' => '选项三',
                    'option4' => '选项四',
                ),
                'default' => 'option1',
            ),

            // 内联显示的单选按钮
            array(
                'id'    => 'inline_radio',
                'type'  => 'radio',
                'title' => '内联单选按钮',
                'desc'  => '水平排列的单选按钮，适合选项较少的场景',
                'options' => array(
                    'small'  => '小号',
                    'medium' => '中号',
                    'large'  => '大号',
                ),
                'inline'  => true,
                'default' => 'medium',
            ),

            // 带图标的单选按钮
            array(
                'id'    => 'icon_radio',
                'type'  => 'radio',
                'title' => '带图标的单选按钮',
                'desc'  => '每个选项都有对应的图标，提升视觉体验',
                'options' => array(
                    'dashboard' => '仪表盘',
                    'posts'     => '文章管理',
                    'media'     => '媒体库',
                    'pages'     => '页面管理',
                    'comments'  => '评论管理',
                    'users'     => '用户管理',
                ),
                'icons' => array(
                    'dashboard' => '<svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path></svg>',
                    'posts'     => '<svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-6a1 1 0 00-1-1H9a1 1 0 00-1 1v6a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd"></path></svg>',
                    'media'     => '<svg class="w-5 h-5 text-purple-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>',
                    'pages'     => '<svg class="w-5 h-5 text-orange-500" fill="currentColor" viewBox="0 0 20 20"><path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z"></path></svg>',
                    'comments'  => '<svg class="w-5 h-5 text-pink-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path></svg>',
                    'users'     => '<svg class="w-5 h-5 text-indigo-500" fill="currentColor" viewBox="0 0 20 20"><path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path></svg>',
                ),
                'color' => 'blue',
                'default' => 'dashboard',
            ),

            // 带描述的单选按钮
            array(
                'id'    => 'description_radio',
                'type'  => 'radio',
                'title' => '带描述的单选按钮',
                'desc'  => '描述信息显示在选项内部，形成卡片式布局',
                'options' => array(
                    'basic'      => '基础套餐',
                    'pro'        => '专业套餐',
                    'business'   => '商业套餐',
                    'enterprise' => '企业套餐',
                ),
                'descriptions' => array(
                    'basic'      => '适合个人用户，包含基础功能和有限存储空间',
                    'pro'        => '适合专业用户，提供高级功能和更多存储空间',
                    'business'   => '适合小型团队，包含协作工具和优先支持',
                    'enterprise' => '适合大型企业，提供定制化解决方案和专属服务',
                ),
                'icons' => array(
                    'basic'      => '<svg class="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg>',
                    'pro'        => '<svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path></svg>',
                    'business'   => '<svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>',
                    'enterprise' => '<svg class="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>',
                ),
                'color' => 'green',
                'default' => 'pro',
            ),

            // 可搜索的单选按钮
            array(
                'id'         => 'searchable_radio',
                'type'       => 'radio',
                'title'      => '可搜索单选按钮',
                'desc'       => '支持搜索过滤的单选按钮，适合选项较多的场景',
                'searchable' => true,
                'show_count' => true,
                'options'    => array(
                    'beijing'    => '北京',
                    'shanghai'   => '上海',
                    'guangzhou'  => '广州',
                    'shenzhen'   => '深圳',
                    'hangzhou'   => '杭州',
                    'nanjing'    => '南京',
                    'wuhan'      => '武汉',
                    'chengdu'    => '成都',
                    'xian'       => '西安',
                    'chongqing'  => '重庆',
                    'tianjin'    => '天津',
                    'dalian'     => '大连',
                    'qingdao'    => '青岛',
                    'ningbo'     => '宁波',
                    'xiamen'     => '厦门',
                ),
                'color' => 'purple',
                'default' => 'beijing',
            ),

            // 分组单选按钮
            array(
                'id'      => 'grouped_radio',
                'type'    => 'radio',
                'title'   => '分组单选按钮',
                'desc'    => '支持选项分组的单选按钮，便于组织大量选项',
                'options' => array(
                    '前端技术' => array(
                        'html'       => 'HTML',
                        'css'        => 'CSS',
                        'javascript' => 'JavaScript',
                        'vue'        => 'Vue.js',
                        'react'      => 'React',
                        'angular'    => 'Angular',
                    ),
                    '后端技术' => array(
                        'php'    => 'PHP',
                        'python' => 'Python',
                        'nodejs' => 'Node.js',
                        'java'   => 'Java',
                        'golang' => 'Go',
                        'rust'   => 'Rust',
                    ),
                    '数据库' => array(
                        'mysql'      => 'MySQL',
                        'postgresql' => 'PostgreSQL',
                        'mongodb'    => 'MongoDB',
                        'redis'      => 'Redis',
                        'sqlite'     => 'SQLite',
                    ),
                ),
                'searchable' => true,
                'color' => 'red',
                'default' => 'php',
            ),

            // 不同颜色主题的单选按钮
            array(
                'id'    => 'colored_radio',
                'type'  => 'radio',
                'title' => '彩色主题单选按钮',
                'desc'  => '展示不同颜色主题的单选按钮样式',
                'options' => array(
                    'blue'   => '蓝色主题',
                    'green'  => '绿色主题',
                    'purple' => '紫色主题',
                    'red'    => '红色主题',
                    'gray'   => '灰色主题',
                ),
                'color' => 'blue',
                'inline' => true,
                'default' => 'blue',
            ),

        ),
    ) );

    // 创建边框设置区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => '边框设置',
        'desc'   => '测试和展示边框字段的各种功能和配置选项',
        'icon'   => 'dashicons-admin-appearance',
        'fields' => array(

            // 基本边框字段
            array(
                'id'    => 'basic_border',
                'type'  => 'border',
                'title' => '基本边框设置',
                'desc'  => '这是一个基本的边框设置字段，支持设置四个方向的边框宽度、样式和颜色',
            ),

            // 带默认值的边框字段
            array(
                'id'      => 'default_border',
                'type'    => 'border',
                'title'   => '带默认值的边框',
                'desc'    => '这个边框字段设置了默认值，展示预设的边框样式',
                'default' => array(
                    'top'    => '2',
                    'right'  => '4',
                    'bottom' => '2',
                    'left'   => '4',
                    'style'  => 'dashed',
                    'color'  => '#3b82f6',
                ),
            ),

            // 只启用左右边框
            array(
                'id'     => 'horizontal_border',
                'type'   => 'border',
                'title'  => '水平边框设置',
                'desc'   => '只显示左右边框的设置选项，适用于垂直分隔线等场景',
                'top'    => false,
                'bottom' => false,
                'default' => array(
                    'left'   => '3',
                    'right'  => '3',
                    'style'  => 'solid',
                    'color'  => '#ef4444',
                ),
            ),

            // 只启用上下边框
            array(
                'id'    => 'vertical_border',
                'type'  => 'border',
                'title' => '垂直边框设置',
                'desc'  => '只显示上下边框的设置选项，适用于水平分隔线等场景',
                'left'  => false,
                'right' => false,
                'default' => array(
                    'top'    => '1',
                    'bottom' => '1',
                    'style'  => 'dotted',
                    'color'  => '#10b981',
                ),
            ),

            // 统一设置模式
            array(
                'id'    => 'unified_border',
                'type'  => 'border',
                'title' => '统一边框设置',
                'desc'  => '使用统一设置模式，一个输入框控制所有方向的边框宽度',
                'all'   => true,
                'default' => array(
                    'all'   => '5',
                    'style' => 'double',
                    'color' => '#8b5cf6',
                ),
            ),

            // 无颜色选择器的边框
            array(
                'id'    => 'no_color_border',
                'type'  => 'border',
                'title' => '无颜色设置的边框',
                'desc'  => '这个边框字段禁用了颜色选择器，只能设置宽度和样式',
                'color' => false,
                'default' => array(
                    'top'    => '3',
                    'right'  => '3',
                    'bottom' => '3',
                    'left'   => '3',
                    'style'  => 'groove',
                ),
            ),

            // 无样式选择器的边框
            array(
                'id'    => 'no_style_border',
                'type'  => 'border',
                'title' => '无样式设置的边框',
                'desc'  => '这个边框字段禁用了样式选择器，只能设置宽度和颜色',
                'style' => false,
                'default' => array(
                    'top'    => '2',
                    'right'  => '2',
                    'bottom' => '2',
                    'left'   => '2',
                    'color'  => '#f59e0b',
                ),
            ),

            // 自定义单位的边框
            array(
                'id'    => 'custom_unit_border',
                'type'  => 'border',
                'title' => '自定义单位边框',
                'desc'  => '这个边框字段使用em作为单位而不是默认的px',
                'unit'  => 'em',
                'default' => array(
                    'top'    => '0.2',
                    'right'  => '0.5',
                    'bottom' => '0.2',
                    'left'   => '0.5',
                    'style'  => 'ridge',
                    'color'  => '#ec4899',
                ),
            ),

        ),
    ) );



    // 创建重复器字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => '重复器字段测试',
        'desc'   => '测试和展示现代化重复器字段的各种功能和配置选项',
        'icon'   => 'dashicons-admin-page',
        'fields' => array(

            // 基础重复器（带动态标题）
            array(
                'id'     => 'basic_repeater',
                'type'   => 'repeater',
                'title'  => '基础重复器',
                'desc'   => '最简单的重复器配置，标题会根据输入的内容实时更新',
                'fields' => array(
                    array(
                        'id'    => 'title',
                        'type'  => 'text',
                        'title' => '标题',
                        'placeholder' => '请输入标题...',
                    ),
                    array(
                        'id'    => 'content',
                        'type'  => 'textarea',
                        'title' => '内容',
                        'placeholder' => '请输入内容...',
                        'rows'  => 3,
                    ),
                ),
                'button_title'   => '添加新项目',
                'max'            => 5,
                'min'            => 1,
                'preview_field'  => 'title',  // 使用title字段作为动态标题
            ),

            // 高级重复器
            array(
                'id'           => 'advanced_repeater',
                'type'         => 'repeater',
                'title'        => '高级重复器',
                'desc'         => '包含多种字段类型的复杂重复器，展示丰富的功能',
                'fields'       => array(
                    array(
                        'id'    => 'name',
                        'type'  => 'text',
                        'title' => '姓名',
                        'placeholder' => '请输入姓名...',
                    ),
                    array(
                        'id'      => 'position',
                        'type'    => 'select',
                        'title'   => '职位',
                        'options' => array(
                            'manager'   => '经理',
                            'developer' => '开发者',
                            'designer'  => '设计师',
                            'tester'    => '测试员',
                        ),
                        'default' => 'developer',
                    ),
                    array(
                        'id'    => 'email',
                        'type'  => 'text',
                        'title' => '邮箱',
                        'placeholder' => '<EMAIL>',
                        'validate' => 'email',
                    ),
                    array(
                        'id'    => 'bio',
                        'type'  => 'textarea',
                        'title' => '个人简介',
                        'placeholder' => '请输入个人简介...',
                        'rows'  => 2,
                    ),
                    array(
                        'id'    => 'active',
                        'type'  => 'checkbox',
                        'title' => '是否激活',
                        'label' => '激活此用户',
                    ),
                ),
                'button_title'  => '添加团队成员',
                'max'           => 10,
                'min'           => 0,
                'sortable'      => true,
                'collapsible'   => true,
                'show_preview'  => true,
                'preview_field' => 'name',
                'color'         => 'green',
            ),

            // 产品列表重复器
            array(
                'id'           => 'products_repeater',
                'type'         => 'repeater',
                'title'        => '产品列表重复器',
                'desc'         => '用于管理产品信息的重复器，包含图片、价格等字段',
                'fields'       => array(
                    array(
                        'id'    => 'product_name',
                        'type'  => 'text',
                        'title' => '产品名称',
                        'placeholder' => '请输入产品名称...',
                    ),
                    array(
                        'id'      => 'category',
                        'type'    => 'radio',
                        'title'   => '产品分类',
                        'options' => array(
                            'electronics' => '电子产品',
                            'clothing'    => '服装',
                            'books'       => '图书',
                            'home'        => '家居用品',
                        ),
                        'inline'  => true,
                        'default' => 'electronics',
                    ),
                    array(
                        'id'    => 'price',
                        'type'  => 'text',
                        'title' => '价格',
                        'placeholder' => '0.00',
                        'before' => '¥',
                        'validate' => 'numeric',
                    ),
                    array(
                        'id'    => 'description',
                        'type'  => 'textarea',
                        'title' => '产品描述',
                        'placeholder' => '请输入产品描述...',
                        'rows'  => 3,
                    ),
                    array(
                        'id'    => 'featured',
                        'type'  => 'checkbox',
                        'title' => '推荐产品',
                        'label' => '设为推荐产品',
                    ),
                ),
                'button_title'  => '添加产品',
                'max'           => 20,
                'min'           => 0,
                'sortable'      => true,
                'collapsible'   => true,
                'show_preview'  => true,
                'preview_field' => 'product_name',
                'color'         => 'purple',
            ),

            // 社交媒体链接重复器
            array(
                'id'           => 'social_links_repeater',
                'type'         => 'repeater',
                'title'        => '社交媒体链接',
                'desc'         => '管理社交媒体链接的简单重复器',
                'fields'       => array(
                    array(
                        'id'      => 'platform',
                        'type'    => 'select',
                        'title'   => '平台',
                        'options' => array(
                            'facebook'  => 'Facebook',
                            'twitter'   => 'Twitter',
                            'instagram' => 'Instagram',
                            'linkedin'  => 'LinkedIn',
                            'youtube'   => 'YouTube',
                            'tiktok'    => 'TikTok',
                            'wechat'    => '微信',
                            'weibo'     => '微博',
                        ),
                        'default' => 'facebook',
                    ),
                    array(
                        'id'    => 'url',
                        'type'  => 'text',
                        'title' => '链接地址',
                        'placeholder' => 'https://...',
                        'validate' => 'url',
                    ),
                    array(
                        'id'    => 'followers',
                        'type'  => 'text',
                        'title' => '粉丝数量',
                        'placeholder' => '0',
                        'validate' => 'numeric',
                    ),
                ),
                'button_title'  => '添加社交链接',
                'max'           => 8,
                'min'           => 0,
                'sortable'      => true,
                'collapsible'   => false,
                'show_preview'  => true,
                'preview_field' => 'platform',
                'color'         => 'blue',
            ),

            // 动态标题演示重复器
            array(
                'id'           => 'dynamic_title_repeater',
                'type'         => 'repeater',
                'title'        => '动态标题演示',
                'desc'         => '演示标题实时更新功能：输入姓名时，项目标题会自动更新',
                'fields'       => array(
                    array(
                        'id'    => 'name',
                        'type'  => 'text',
                        'title' => '姓名',
                        'placeholder' => '请输入姓名...',
                    ),
                    array(
                        'id'      => 'position',
                        'type'    => 'select',
                        'title'   => '职位',
                        'options' => array(
                            'manager'   => '经理',
                            'developer' => '开发者',
                            'designer'  => '设计师',
                            'tester'    => '测试员',
                        ),
                        'default' => 'developer',
                    ),
                    array(
                        'id'    => 'email',
                        'type'  => 'text',
                        'title' => '邮箱',
                        'placeholder' => '<EMAIL>',
                        'validate' => 'email',
                    ),
                ),
                'button_title'  => '添加成员',
                'max'           => 10,
                'min'           => 0,
                'sortable'      => true,
                'collapsible'   => true,
                'preview_field' => 'name',  // 使用name字段作为动态标题
                'color'         => 'green',
            ),

            // 嵌套字段重复器
            array(
                'id'           => 'nested_repeater',
                'type'         => 'repeater',
                'title'        => '嵌套字段重复器',
                'desc'         => '包含复杂嵌套字段的重复器示例',
                'fields'       => array(
                    array(
                        'id'    => 'section_title',
                        'type'  => 'text',
                        'title' => '区块标题',
                        'placeholder' => '请输入区块标题...',
                    ),
                    array(
                        'id'      => 'layout_type',
                        'type'    => 'radio',
                        'title'   => '布局类型',
                        'options' => array(
                            'single'  => '单列布局',
                            'double'  => '双列布局',
                            'triple'  => '三列布局',
                            'grid'    => '网格布局',
                        ),
                        'default' => 'single',
                    ),
                    array(
                        'id'      => 'background_color',
                        'type'    => 'color',
                        'title'   => '背景颜色',
                        'default' => '#ffffff',
                    ),
                    array(
                        'id'    => 'custom_css',
                        'type'  => 'textarea',
                        'title' => '自定义CSS',
                        'placeholder' => '/* 自定义样式 */',
                        'rows'  => 4,
                    ),
                    array(
                        'id'    => 'visible',
                        'type'  => 'checkbox',
                        'title' => '显示设置',
                        'label' => '在前端显示此区块',
                        'default' => true,
                    ),
                ),
                'button_title'  => '添加内容区块',
                'max'           => 15,
                'min'           => 0,
                'sortable'      => true,
                'collapsible'   => true,
                'show_preview'  => true,
                'preview_field' => 'section_title',
                'color'         => 'red',
            ),

        ),
    ) );

    // 创建手风琴字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => '手风琴字段测试',
        'desc'   => '测试和展示现代化手风琴字段的各种功能和配置选项',
        'icon'   => 'dashicons-menu-alt3',
        'fields' => array(

            // 基础手风琴
            array(
                'id'         => 'basic_accordion',
                'type'       => 'accordion',
                'title'      => '基础手风琴',
                'desc'       => '最简单的手风琴配置，包含基本的设置选项',
                'accordions' => array(
                    array(
                        'title'  => '网站基础设置',
                        'icon'   => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path></svg>',
                        'open'   => true,
                        'fields' => array(
                            array(
                                'id'    => 'site_title',
                                'type'  => 'text',
                                'title' => '网站标题',
                                'placeholder' => '请输入网站标题...',
                            ),
                            array(
                                'id'    => 'site_tagline',
                                'type'  => 'text',
                                'title' => '网站副标题',
                                'placeholder' => '请输入网站副标题...',
                            ),
                            array(
                                'id'    => 'site_description',
                                'type'  => 'textarea',
                                'title' => '网站描述',
                                'placeholder' => '请输入网站描述...',
                                'rows'  => 3,
                            ),
                        ),
                    ),
                    array(
                        'title'  => '联系信息',
                        'icon'   => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path><path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path></svg>',
                        'fields' => array(
                            array(
                                'id'    => 'contact_email',
                                'type'  => 'text',
                                'title' => '联系邮箱',
                                'placeholder' => '<EMAIL>',
                                'validate' => 'email',
                            ),
                            array(
                                'id'    => 'contact_phone',
                                'type'  => 'text',
                                'title' => '联系电话',
                                'placeholder' => '+86 138 0000 0000',
                            ),
                            array(
                                'id'    => 'contact_address',
                                'type'  => 'textarea',
                                'title' => '联系地址',
                                'placeholder' => '请输入详细地址...',
                                'rows'  => 2,
                            ),
                        ),
                    ),
                    array(
                        'title'  => '社交媒体',
                        'icon'   => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"></path></svg>',
                        'fields' => array(
                            array(
                                'id'    => 'facebook_url',
                                'type'  => 'text',
                                'title' => 'Facebook',
                                'placeholder' => 'https://facebook.com/yourpage',
                            ),
                            array(
                                'id'    => 'twitter_url',
                                'type'  => 'text',
                                'title' => 'Twitter',
                                'placeholder' => 'https://twitter.com/youraccount',
                            ),
                            array(
                                'id'    => 'instagram_url',
                                'type'  => 'text',
                                'title' => 'Instagram',
                                'placeholder' => 'https://instagram.com/youraccount',
                            ),
                        ),
                    ),
                ),
                'multiple'    => false,
                'collapsible' => true,
                'color'       => 'blue',
                'size'        => 'default',
            ),

            // 多选手风琴
            array(
                'id'         => 'multiple_accordion',
                'type'       => 'accordion',
                'title'      => '多选手风琴',
                'desc'       => '允许同时展开多个项目的手风琴',
                'accordions' => array(
                    array(
                        'title'  => '外观设置',
                        'icon'   => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zM3 15a1 1 0 011-1h1a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1z" clip-rule="evenodd"></path><path d="M16 5a1 1 0 011-1h1a1 1 0 011 1v4a1 1 0 01-1 1h-1a1 1 0 01-1-1V5zM19 13a1 1 0 00-1 1v1a1 1 0 001 1h1a1 1 0 001-1v-1a1 1 0 00-1-1h-1z"></path></svg>',
                        'open'   => true,
                        'fields' => array(
                            array(
                                'id'      => 'theme_color',
                                'type'    => 'color',
                                'title'   => '主题颜色',
                                'default' => '#3b82f6',
                            ),
                            array(
                                'id'      => 'layout_style',
                                'type'    => 'radio',
                                'title'   => '布局样式',
                                'options' => array(
                                    'boxed'     => '盒式布局',
                                    'fullwidth' => '全宽布局',
                                    'fluid'     => '流体布局',
                                ),
                                'default' => 'boxed',
                            ),
                        ),
                    ),

                    array(
                        'title'  => '性能优化',
                        'icon'   => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path></svg>',
                        'open'   => true,
                        'fields' => array(
                            array(
                                'id'      => 'enable_cache',
                                'type'    => 'checkbox',
                                'title'   => '启用缓存',
                                'options' => array(
                                    'enabled' => '启用页面缓存以提高加载速度',
                                ),
                                'default' => array(),
                            ),
                            array(
                                'id'      => 'minify_css',
                                'type'    => 'checkbox',
                                'title'   => '压缩CSS',
                                'options' => array(
                                    'enabled' => '压缩CSS文件以减少文件大小',
                                ),
                                'default' => array(),
                            ),
                            array(
                                'id'      => 'minify_js',
                                'type'    => 'checkbox',
                                'title'   => '压缩JavaScript',
                                'options' => array(
                                    'enabled' => '压缩JavaScript文件以减少文件大小',
                                ),
                                'default' => array(),
                            ),
                        ),
                    ),
                    array(
                        'title'  => 'SEO设置',
                        'icon'   => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path></svg>',
                        'fields' => array(
                            array(
                                'id'    => 'meta_description',
                                'type'  => 'textarea',
                                'title' => 'Meta描述',
                                'placeholder' => '请输入网站的Meta描述...',
                                'rows'  => 3,
                            ),
                            array(
                                'id'    => 'meta_keywords',
                                'type'  => 'text',
                                'title' => 'Meta关键词',
                                'placeholder' => '关键词1, 关键词2, 关键词3',
                            ),
                        ),
                    ),
                ),
                'multiple'    => true,
                'collapsible' => true,
                'color'       => 'green',
                'size'        => 'default',
            ),

            // 大尺寸手风琴
            array(
                'id'         => 'large_accordion',
                'type'       => 'accordion',
                'title'      => '大尺寸手风琴',
                'desc'       => '使用大尺寸样式的手风琴字段',
                'accordions' => array(
                    array(
                        'title'  => '高级配置',
                        'icon'   => '<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path></svg>',
                        'open'   => true,
                        'fields' => array(
                            array(
                                'id'    => 'custom_css',
                                'type'  => 'textarea',
                                'title' => '自定义CSS',
                                'placeholder' => '/* 在这里输入自定义CSS代码 */',
                                'rows'  => 8,
                            ),
                            array(
                                'id'    => 'custom_js',
                                'type'  => 'textarea',
                                'title' => '自定义JavaScript',
                                'placeholder' => '// 在这里输入自定义JavaScript代码',
                                'rows'  => 8,
                            ),
                        ),
                    ),
                ),
                'multiple'    => false,
                'collapsible' => true,
                'color'       => 'purple',
                'size'        => 'large',
                'rounded'     => true,
                'shadow'      => true,
            ),

            // 小尺寸手风琴
            array(
                'id'         => 'small_accordion',
                'type'       => 'accordion',
                'title'      => '小尺寸手风琴',
                'desc'       => '使用小尺寸样式的紧凑手风琴字段',
                'accordions' => array(
                    array(
                        'title'  => '快速设置',
                        'fields' => array(
                            array(
                                'id'      => 'quick_toggle1',
                                'type'    => 'checkbox',
                                'title'   => '选项1',
                                'options' => array(
                                    'enabled' => '启用选项1',
                                ),
                                'default' => array(),
                            ),
                            array(
                                'id'      => 'quick_toggle2',
                                'type'    => 'checkbox',
                                'title'   => '选项2',
                                'options' => array(
                                    'enabled' => '启用选项2',
                                ),
                                'default' => array(),
                            ),
                        ),
                    ),
                    array(
                        'title'  => '其他设置',
                        'fields' => array(
                            array(
                                'id'    => 'other_setting',
                                'type'  => 'text',
                                'title' => '其他设置',
                                'placeholder' => '请输入值...',
                            ),
                        ),
                    ),
                ),
                'multiple'    => true,
                'collapsible' => true,
                'color'       => 'red',
                'size'        => 'small',
            ),

        ),
    ) );


    // 创建按钮字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => '按钮字段测试',
        'desc'   => '测试和展示现代化按钮选择字段的各种功能和配置选项',
        'icon'   => 'dashicons-button',
        'fields' => array(

            // 基本单选按钮
            array(
                'id'    => 'basic_single_button',
                'type'  => 'button',
                'title' => '基本单选按钮',
                'desc'  => '最简单的单选按钮组，适用于基础选择场景',
                'options' => array(
                    'option1' => '选项一',
                    'option2' => '选项二',
                    'option3' => '选项三',
                    'option4' => '选项四',
                ),
                'default' => 'option1',
            ),

            // 多选按钮
            array(
                'id'       => 'multiple_button',
                'type'     => 'button',
                'title'    => '多选按钮组',
                'desc'     => '支持选择多个选项的按钮组',
                'multiple' => true,
                'options'  => array(
                    'html'   => 'HTML',
                    'css'    => 'CSS',
                    'js'     => 'JavaScript',
                    'php'    => 'PHP',
                    'python' => 'Python',
                    'java'   => 'Java',
                ),
                'default' => array( 'html', 'css' ),
            ),

            // 带图标的按钮
            array(
                'id'    => 'icon_button',
                'type'  => 'button',
                'title' => '带图标的按钮',
                'desc'  => '每个选项都有对应的图标，提升视觉体验',
                'options' => array(
                    'dashboard' => '仪表盘',
                    'posts'     => '文章',
                    'media'     => '媒体',
                    'pages'     => '页面',
                    'comments'  => '评论',
                    'users'     => '用户',
                ),
                'icons' => array(
                    'dashboard' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path></svg>',
                    'posts'     => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-6a1 1 0 00-1-1H9a1 1 0 00-1 1v6a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd"></path></svg>',
                    'media'     => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>',
                    'pages'     => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z"></path></svg>',
                    'comments'  => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path></svg>',
                    'users'     => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path></svg>',
                ),
                'color' => 'purple',
                'default' => 'dashboard',
            ),

            // 带描述的按钮
            array(
                'id'    => 'description_button',
                'type'  => 'button',
                'title' => '带描述的按钮',
                'desc'  => '描述信息显示在按钮内部，形成卡片式布局',
                'options' => array(
                    'basic'    => '基础套餐',
                    'pro'      => '专业套餐',
                    'business' => '商业套餐',
                    'enterprise' => '企业套餐',
                ),
                'descriptions' => array(
                    'basic'    => '适合个人用户，包含基础功能和有限存储空间',
                    'pro'      => '适合专业用户，提供高级功能和更多存储空间',
                    'business' => '适合小型团队，包含协作工具和优先支持',
                    'enterprise' => '适合大型企业，提供定制化解决方案和专属服务',
                ),
                'icons' => array(
                    'basic'    => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path></svg>',
                    'pro'      => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path></svg>',
                    'business' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm3 2h2v4H7V6zm2 6H7v2h2v-2zm2-6h2v2h-2V6zm2 4h-2v2h2v-2z" clip-rule="evenodd"></path></svg>',
                    'enterprise' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path><path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path></svg>',
                ),
                'color' => 'green',
                'style' => 'card',
                'size'  => 'large',
                'default' => 'basic',
            ),

            // 不同样式的按钮
            array(
                'id'    => 'style_button',
                'type'  => 'button',
                'title' => '标准样式按钮',
                'desc'  => '展示基础的按钮样式',
                'options' => array(
                    'default' => '默认样式',
                    'outline' => '轮廓样式',
                    'ghost'   => '幽灵样式',
                ),
                'style' => 'default',
                'color' => 'blue',
                'default' => 'default',
            ),

            // 特效样式按钮
            array(
                'id'    => 'effect_button',
                'type'  => 'button',
                'title' => '特效样式按钮',
                'desc'  => '展示带有特殊效果的按钮样式',
                'options' => array(
                    'gradient' => '渐变按钮',
                    'glass'    => '玻璃按钮',
                    'neon'     => '霓虹按钮',
                    'raised'   => '凸起按钮',
                    'floating' => '浮动按钮',
                ),
                'style' => 'gradient',
                'color' => 'purple',
                'default' => 'gradient',
            ),

            // 创意样式按钮
            array(
                'id'    => 'creative_button',
                'type'  => 'button',
                'title' => '创意样式按钮',
                'desc'  => '展示独特创意的按钮样式',
                'options' => array(
                    'neumorphism' => '新拟物按钮',
                    'brutalist'   => '野兽派按钮',
                    'minimal'     => '极简按钮',
                    'card'        => '卡片按钮',
                ),
                'style' => 'neumorphism',
                'color' => 'blue',
                'default' => 'neumorphism',
            ),

            // 形状样式按钮
            array(
                'id'    => 'shape_button',
                'type'  => 'button',
                'title' => '形状样式按钮',
                'desc'  => '展示不同形状的按钮',
                'options' => array(
                    'pill'  => '药丸按钮',
                    'flat'  => '扁平按钮',
                    'retro' => '复古按钮',
                ),
                'style' => 'pill',
                'color' => 'green',
                'size'  => 'large',
                'default' => 'pill',
            ),

            // 带搜索功能的按钮
            array(
                'id'       => 'search_button',
                'type'     => 'button',
                'title'    => '带搜索功能的按钮',
                'desc'     => '技术栈选择器，支持搜索和多选',
                'multiple' => true,
                'search'   => false,
                'options'  => array(
                    'react'      => 'React',
                    'vue'        => 'Vue.js',
                    'angular'    => 'Angular',
                    'svelte'     => 'Svelte',
                    'jquery'     => 'jQuery',
                    'bootstrap'  => 'Bootstrap',
                    'tailwind'   => 'Tailwind CSS',
                    'bulma'      => 'Bulma',
                    'foundation' => 'Foundation',
                    'semantic'   => 'Semantic UI',
                    'sass'       => 'Sass',
                    'less'       => 'Less',
                    'webpack'    => 'Webpack',
                    'vite'       => 'Vite',
                    'rollup'     => 'Rollup',
                    'parcel'     => 'Parcel',
                ),

                'color' => 'blue',
                'style' => 'pill',
                'size'  => 'small',
                'columns' => 'auto',
                'default' => array( 'react', 'tailwind', 'vue' ),
            ),

            // 网格布局按钮
            array(
                'id'    => 'grid_layout_button',
                'type'  => 'button',
                'title' => '网格布局按钮',
                'desc'  => '使用固定的3列网格布局展示选项',
                'options' => array(
                    'layout1' => '经典布局',
                    'layout2' => '现代布局',
                    'layout3' => '简约布局',
                    'layout4' => '卡片布局',
                    'layout5' => '列表布局',
                    'layout6' => '网格布局',
                ),
                'icons' => array(
                    'layout1' => '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path></svg>',
                    'layout2' => '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-4l-2 2-2-2H5a2 2 0 01-2-2V5zm5.5 6a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zm2.5 0a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zm2.5 0a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" clip-rule="evenodd"></path></svg>',
                    'layout3' => '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg>',
                    'layout4' => '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"></path></svg>',
                    'layout5' => '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg>',
                    'layout6' => '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" clip-rule="evenodd"></path></svg>',
                ),
                'columns' => 'auto',
                'color' => 'purple',
                'style' => 'outline',
                'size' => 'small',
                'default' => 'layout1',
            ),

            // 紧凑布局按钮
            array(
                'id'    => 'compact_button',
                'type'  => 'button',
                'title' => '紧凑布局按钮',
                'desc'  => '使用自动布局，按钮大小自适应内容',
                'multiple' => true,
                'options' => array(
                    'xs' => 'XS',
                    'sm' => 'SM',
                    'md' => 'MD',
                    'lg' => 'LG',
                    'xl' => 'XL',
                    '2xl' => '2XL',
                    '3xl' => '3XL',
                    '4xl' => '4XL',
                ),
                'columns' => 'auto',
                'color' => 'gray',
                'style' => 'pill',
                'size' => 'small',
                'default' => array( 'md', 'lg' ),
            ),

        ),
    ) );

    // 创建Checkbox字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => 'Checkbox字段测试',
        'desc'   => '展示强大的复选框字段功能，支持多种样式、搜索、批量操作等高级功能',
        'icon'   => 'dashicons-yes-alt',
        'fields' => array(

            // 基本复选框
            array(
                'id'      => 'basic_checkbox',
                'type'    => 'checkbox',
                'title'   => '基本复选框',
                'desc'    => '最简单的复选框，展示基本功能',
                'style'   => 'list',
                'searchable' => false,
                'select_all' => false,
                'show_count' => false,
                'options' => array(
                    'option1' => '选项1',
                    'option2' => '选项2',
                    'option3' => '选项3',
                    'option4' => '选项4',
                ),
                'default' => array( 'option1' ),
            ),

            // 带搜索和全选的复选框
            array(
                'id'      => 'searchable_checkbox',
                'type'    => 'checkbox',
                'title'   => '带搜索功能的复选框',
                'desc'    => '支持搜索、全选、清空等高级功能',
                'searchable'  => true,
                'select_all'  => true,
                'show_count'  => true,
                'options' => array(
                    'html'       => 'HTML',
                    'css'        => 'CSS',
                    'javascript' => 'JavaScript',
                    'php'        => 'PHP',
                    'python'     => 'Python',
                    'nodejs'     => 'Node.js',
                    'react'      => 'React',
                    'vue'        => 'Vue.js',
                    'laravel'    => 'Laravel',
                    'wordpress'  => 'WordPress',
                    'mysql'      => 'MySQL',
                    'mongodb'    => 'MongoDB',
                ),
                'default' => array( 'html', 'css' ),
            ),

            // 带验证的复选框
            array(
                'id'      => 'validated_checkbox',
                'type'    => 'checkbox',
                'title'   => '带验证的复选框',
                'desc'    => '限制最少选择2项，最多选择4项',
                'style'   => 'list',
                'required_min' => 2,
                'required_max' => 4,
                'show_count'   => true,
                'searchable' => false,
                'select_all' => false,
                'options' => array(
                    'email'    => '邮件营销',
                    'social'   => '社交媒体',
                    'seo'      => 'SEO优化',
                    'content'  => '内容营销',
                    'ppc'      => '付费广告',
                    'affiliate' => '联盟营销',
                ),
                'default' => array( 'email', 'seo' ),
            ),

        ),
    ) );

    // 创建代码编辑器字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => '代码编辑器字段测试',
        'desc'   => '展示现代化的代码编辑器字段功能，基于Monaco Editor，支持语法高亮、代码折叠、自动补全等高级功能',
        'icon'   => 'dashicons-editor-code',
        'fields' => array(

            // JavaScript代码编辑器
            array(
                'id'    => 'code_javascript',
                'type'  => 'code',
                'title' => 'JavaScript代码编辑器',
                'desc'  => '现代化的JavaScript代码编辑器，支持语法高亮、自动补全、代码折叠等功能',
                'language' => 'javascript',
                'theme' => 'vs-dark',
                'height' => '400px',
                'toolbar' => true,
                'language_selector' => true,
                'theme_switcher' => true,
                'minimap' => true,
                'folding' => true,
                'formatOnPaste' => true,
                'autoClosingBrackets' => 'always',
                'default' => '// Xun Framework JavaScript 示例\nfunction initXunFramework() {\n    console.log("Xun Framework 初始化中...");\n    \n    // 配置对象\n    const config = {\n        theme: "dark",\n        ajaxSave: true,\n        showReset: true\n    };\n    \n    // 初始化框架\n    if (typeof XunFramework !== "undefined") {\n        const xun = new XunFramework(config);\n        xun.init();\n        console.log("Xun Framework 初始化完成！");\n    }\n}\n\n// 页面加载完成后初始化\ndocument.addEventListener("DOMContentLoaded", initXunFramework);',
            ),

        ),
    ) );

    // 创建 Sorter 排序器字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => 'Sorter 排序器字段测试',
        'desc'   => '展示现代化的拖拽排序功能，支持启用/禁用项目、批量操作、实时预览等高级特性',
        'icon'   => 'dashicons-sort',
        'fields' => array(

            // 基础排序器字段
            array(
                'id'      => 'basic_sorter',
                'type'    => 'sorter',
                'title'   => '基础排序器',
                'desc'    => '最简单的排序器配置，支持拖拽排序和启用/禁用功能',
                'options' => array(
                    'header'    => '页面头部',
                    'menu'      => '导航菜单',
                    'content'   => '主要内容',
                    'sidebar'   => '侧边栏',
                    'footer'    => '页面底部',
                ),
                'default' => array(
                    'enabled' => array(
                        'header'  => '页面头部',
                        'menu'    => '导航菜单',
                        'content' => '主要内容',
                        'footer'  => '页面底部',
                    ),
                    'disabled' => array(
                        'sidebar' => '侧边栏',
                    ),
                ),
            ),

            // 高级排序器字段
            array(
                'id'              => 'advanced_sorter',
                'type'            => 'sorter',
                'title'           => '高级排序器',
                'desc'            => '包含所有高级功能的排序器：批量操作、实时预览、自定义标题等',
                'enabled_title'   => '已激活的功能模块',
                'disabled_title'  => '可选功能模块',
                'allow_all_none'  => true,
                'show_preview'    => true,
                'options'         => array(
                    'breadcrumb'    => '面包屑导航',
                    'search'        => '搜索功能',
                    'social_share'  => '社交分享',
                    'comments'      => '评论系统',
                    'related_posts' => '相关文章',
                    'author_bio'    => '作者简介',
                    'tags'          => '标签云',
                    'newsletter'    => '邮件订阅',
                    'back_to_top'   => '返回顶部',
                    'reading_time'  => '阅读时间',
                ),
                'default' => array(
                    'enabled' => array(
                        'breadcrumb'   => '面包屑导航',
                        'search'       => '搜索功能',
                        'comments'     => '评论系统',
                        'tags'         => '标签云',
                        'back_to_top'  => '返回顶部',
                    ),
                    'disabled' => array(
                        'social_share'  => '社交分享',
                        'related_posts' => '相关文章',
                        'author_bio'    => '作者简介',
                        'newsletter'    => '邮件订阅',
                        'reading_time'  => '阅读时间',
                    ),
                ),
            ),

            // 仅启用区域的排序器
            array(
                'id'       => 'enabled_only_sorter',
                'type'     => 'sorter',
                'title'    => '仅启用区域排序器',
                'desc'     => '只显示启用区域，适用于纯排序场景（不需要启用/禁用切换）',
                'disabled' => false,
                'options'  => array(
                    'step1' => '第一步：准备工作',
                    'step2' => '第二步：开始配置',
                    'step3' => '第三步：测试功能',
                    'step4' => '第四步：发布上线',
                ),
                'default' => array(
                    'enabled' => array(
                        'step1' => '第一步：准备工作',
                        'step2' => '第二步：开始配置',
                        'step3' => '第三步：测试功能',
                        'step4' => '第四步：发布上线',
                    ),
                ),
            ),

            // 小型排序器字段
            array(
                'id'      => 'mini_sorter',
                'type'    => 'sorter',
                'title'   => '小型排序器',
                'desc'    => '适用于少量选项的排序器，界面更加紧凑',
                'options' => array(
                    'title'   => '文章标题',
                    'excerpt' => '文章摘要',
                    'meta'    => '文章信息',
                ),
                'default' => array(
                    'enabled' => array(
                        'title' => '文章标题',
                        'meta'  => '文章信息',
                    ),
                    'disabled' => array(
                        'excerpt' => '文章摘要',
                    ),
                ),
            ),

        ),
    ) );

    // 创建 Slider 滑块字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => 'Slider 滑块字段测试',
        'desc'   => '展示现代化的滑块功能，支持单值和范围选择、实时预览、键盘快捷键等高级特性',
        'icon'   => 'dashicons-leftright',
        'fields' => array(

            // 基础滑块字段
            array(
                'id'      => 'basic_slider',
                'type'    => 'slider',
                'title'   => '基础滑块',
                'desc'    => '最简单的滑块配置，支持0-100的数值选择',
                'min'     => 0,
                'max'     => 100,
                'step'    => 1,
                'default' => 50,
            ),

            // 带单位的滑块
            array(
                'id'      => 'percentage_slider',
                'type'    => 'slider',
                'title'   => '百分比滑块',
                'desc'    => '带有百分比单位显示的滑块',
                'min'     => 0,
                'max'     => 100,
                'step'    => 5,
                'unit'    => '%',
                'default' => 75,
                'color'   => 'green',
            ),

            // 范围滑块
            array(
                'id'           => 'range_slider',
                'type'         => 'slider',
                'title'        => '范围滑块',
                'desc'         => '支持选择数值范围的双手柄滑块',
                'min'          => 0,
                'max'          => 1000,
                'step'         => 10,
                'range'        => true,
                'unit'         => 'px',
                'show_preview' => true,
                'default'      => array(
                    'min' => 200,
                    'max' => 800,
                ),
                'color'        => 'purple',
            ),

            // 高精度滑块
            array(
                'id'        => 'precision_slider',
                'type'      => 'slider',
                'title'     => '高精度滑块',
                'desc'      => '支持小数点精度的滑块，适用于精确数值控制',
                'min'       => 0,
                'max'       => 10,
                'step'      => 0.1,
                'precision' => 2,
                'unit'      => 'em',
                'default'   => 1.5,
                'color'     => 'blue',
            ),

            // 带刻度的滑块
            array(
                'id'          => 'ticks_slider',
                'type'        => 'slider',
                'title'       => '带刻度滑块',
                'desc'        => '显示刻度标记的滑块，便于精确选择',
                'min'         => 0,
                'max'         => 50,
                'step'        => 5,
                'show_ticks'  => true,
                'tick_step'   => 10,
                'show_labels' => true,
                'default'     => 25,
                'color'       => 'red',
            ),

            // 自定义样式滑块
            array(
                'id'           => 'custom_slider',
                'type'         => 'slider',
                'title'        => '自定义样式滑块',
                'desc'         => '展示各种自定义配置选项的滑块',
                'min'          => -100,
                'max'          => 100,
                'step'         => 1,
                'prefix'       => '±',
                'suffix'       => '°',
                'show_preview' => true,
                'keyboard'     => true,
                'tooltip'      => true,
                'animate'      => true,
                'color'        => 'indigo',
                'size'         => 'large',
                'default'      => 0,
            ),

            // 温度范围滑块
            array(
                'id'           => 'temperature_range',
                'type'         => 'slider',
                'title'        => '温度范围滑块',
                'desc'         => '模拟温度控制的范围滑块示例',
                'min'          => -20,
                'max'          => 50,
                'step'         => 1,
                'range'        => true,
                'unit'         => '°C',
                'show_preview' => true,
                'show_ticks'   => true,
                'tick_step'    => 10,
                'default'      => array(
                    'min' => 18,
                    'max' => 26,
                ),
                'color'        => 'orange',
            ),

            // 音量控制滑块
            array(
                'id'           => 'volume_slider',
                'type'         => 'slider',
                'title'        => '音量控制滑块',
                'desc'         => '模拟音量控制的滑块，支持静音到最大音量',
                'min'          => 0,
                'max'          => 100,
                'step'         => 1,
                'unit'         => '%',
                'show_preview' => true,
                'tooltip'      => true,
                'default'      => 80,
                'color'        => 'cyan',
                'format_value' => 'function(value) { return value === 0 ? "静音" : value + "%"; }',
            ),

            // 隐藏输入框的滑块
            array(
                'id'         => 'no_input_slider',
                'type'       => 'slider',
                'title'      => '纯滑块控制',
                'desc'       => '隐藏数值输入框，只通过滑块操作',
                'min'        => 1,
                'max'        => 10,
                'step'       => 1,
                'show_input' => false,
                'tooltip'    => true,
                'default'    => 5,
                'color'      => 'pink',
            ),

        ),
    ) );

    // 创建 Palette 调色板字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => 'Palette 调色板字段测试',
        'desc'   => '展示现代化的调色板功能，支持颜色选择、搜索过滤、自定义颜色等高级特性',
        'icon'   => 'dashicons-art',
        'fields' => array(

            // 基础调色板字段
            array(
                'id'      => 'basic_palette',
                'type'    => 'palette',
                'title'   => '基础调色板',
                'desc'    => '简单的颜色选择调色板，支持单选模式',
                'options' => array(
                    '#FF6B6B' => '#FF6B6B', // 珊瑚红
                    '#4ECDC4' => '#4ECDC4', // 青绿色
                    '#45B7D1' => '#45B7D1', // 天蓝色
                    '#96CEB4' => '#96CEB4', // 薄荷绿
                    '#FFEAA7' => '#FFEAA7', // 柠檬黄
                    '#DDA0DD' => '#DDA0DD', // 紫罗兰
                    '#98D8C8' => '#98D8C8', // 海绿色
                    '#F7DC6F' => '#F7DC6F', // 金黄色
                    '#BB8FCE' => '#BB8FCE', // 淡紫色
                    '#85C1E9' => '#85C1E9', // 浅蓝色
                ),
                'default' => '#4ECDC4',
            ),

            // 多选调色板
            array(
                'id'       => 'multiple_palette',
                'type'     => 'palette',
                'title'    => '多选调色板',
                'desc'     => '支持选择多个颜色的调色板',
                'multiple' => true,
                'options'  => array(
                    '#E74C3C' => '#E74C3C', // 红色
                    '#3498DB' => '#3498DB', // 蓝色
                    '#2ECC71' => '#2ECC71', // 绿色
                    '#F39C12' => '#F39C12', // 橙色
                    '#9B59B6' => '#9B59B6', // 紫色
                    '#1ABC9C' => '#1ABC9C', // 青色
                    '#E67E22' => '#E67E22', // 胡萝卜色
                    '#34495E' => '#34495E', // 深蓝灰
                    '#95A5A6' => '#95A5A6', // 灰色
                    '#F1C40F' => '#F1C40F', // 黄色
                ),
                'default' => array( '#3498DB', '#2ECC71', '#F39C12' ),
            ),

            // 分组调色板
            array(
                'id'           => 'grouped_palette',
                'type'         => 'palette',
                'title'        => '分组调色板',
                'desc'         => '按主题分组的调色板，便于颜色管理',
                'group_colors' => true,
                'show_labels'  => true,
                'options'      => array(
                    'warm' => array(
                        'label'  => '暖色调',
                        'colors' => array(
                            '#FF6B6B' => '#FF6B6B', // 珊瑚红
                            '#FF8E53' => '#FF8E53', // 橙红色
                            '#FF6348' => '#FF6348', // 番茄红
                            '#FFBE0B' => '#FFBE0B', // 金橙色
                            '#FB8500' => '#FB8500', // 橙色
                        ),
                    ),
                    'cool' => array(
                        'label'  => '冷色调',
                        'colors' => array(
                            '#4ECDC4' => '#4ECDC4', // 青绿色
                            '#45B7D1' => '#45B7D1', // 天蓝色
                            '#96CEB4' => '#96CEB4', // 薄荷绿
                            '#219EBC' => '#219EBC', // 海蓝色
                            '#8ECAE6' => '#8ECAE6', // 浅蓝色
                        ),
                    ),
                    'neutral' => array(
                        'label'  => '中性色调',
                        'colors' => array(
                            '#6C757D' => '#6C757D', // 灰色
                            '#ADB5BD' => '#ADB5BD', // 浅灰色
                            '#495057' => '#495057', // 深灰色
                            '#F8F9FA' => '#F8F9FA', // 极浅灰
                            '#343A40' => '#343A40', // 深色
                        ),
                    ),
                ),
                'default' => '#45B7D1',
            ),

            // 带搜索的调色板
            array(
                'id'           => 'searchable_palette',
                'type'         => 'palette',
                'title'        => '可搜索调色板',
                'desc'         => '支持颜色搜索和过滤的大型调色板',
                'show_search'  => true,
                'colors_per_row' => 8,
                'options'      => array(
                    // Material Design 颜色
                    '#F44336' => array( 'value' => '#F44336', 'label' => 'Red 500' ),
                    '#E91E63' => array( 'value' => '#E91E63', 'label' => 'Pink 500' ),
                    '#9C27B0' => array( 'value' => '#9C27B0', 'label' => 'Purple 500' ),
                    '#673AB7' => array( 'value' => '#673AB7', 'label' => 'Deep Purple 500' ),
                    '#3F51B5' => array( 'value' => '#3F51B5', 'label' => 'Indigo 500' ),
                    '#2196F3' => array( 'value' => '#2196F3', 'label' => 'Blue 500' ),
                    '#03A9F4' => array( 'value' => '#03A9F4', 'label' => 'Light Blue 500' ),
                    '#00BCD4' => array( 'value' => '#00BCD4', 'label' => 'Cyan 500' ),
                    '#009688' => array( 'value' => '#009688', 'label' => 'Teal 500' ),
                    '#4CAF50' => array( 'value' => '#4CAF50', 'label' => 'Green 500' ),
                    '#8BC34A' => array( 'value' => '#8BC34A', 'label' => 'Light Green 500' ),
                    '#CDDC39' => array( 'value' => '#CDDC39', 'label' => 'Lime 500' ),
                    '#FFEB3B' => array( 'value' => '#FFEB3B', 'label' => 'Yellow 500' ),
                    '#FFC107' => array( 'value' => '#FFC107', 'label' => 'Amber 500' ),
                    '#FF9800' => array( 'value' => '#FF9800', 'label' => 'Orange 500' ),
                    '#FF5722' => array( 'value' => '#FF5722', 'label' => 'Deep Orange 500' ),
                ),
                'default' => '#2196F3',
            ),

            // 自定义颜色调色板
            array(
                'id'          => 'custom_palette',
                'type'        => 'palette',
                'title'       => '自定义颜色调色板',
                'desc'        => '支持添加自定义颜色的调色板',
                'show_custom' => true,
                'multiple'    => true,
                'options'     => array(
                    '#FF0000' => '#FF0000', // 红色
                    '#00FF00' => '#00FF00', // 绿色
                    '#0000FF' => '#0000FF', // 蓝色
                    '#FFFF00' => '#FFFF00', // 黄色
                    '#FF00FF' => '#FF00FF', // 洋红
                    '#00FFFF' => '#00FFFF', // 青色
                ),
                'default' => array( '#FF0000', '#00FF00' ),
            ),

            // 列表布局调色板
            array(
                'id'      => 'list_palette',
                'type'    => 'palette',
                'title'   => '列表布局调色板',
                'desc'    => '使用列表布局显示颜色信息的调色板',
                'layout'  => 'list',
                'options' => array(
                    '#1F2937' => array( 'value' => '#1F2937', 'label' => '深灰色', 'desc' => '适用于深色主题的背景色' ),
                    '#374151' => array( 'value' => '#374151', 'label' => '中灰色', 'desc' => '适用于次要文本和边框' ),
                    '#6B7280' => array( 'value' => '#6B7280', 'label' => '浅灰色', 'desc' => '适用于辅助文本' ),
                    '#D1D5DB' => array( 'value' => '#D1D5DB', 'label' => '极浅灰', 'desc' => '适用于分割线和背景' ),
                    '#F9FAFB' => array( 'value' => '#F9FAFB', 'label' => '白灰色', 'desc' => '适用于浅色主题背景' ),
                ),
                'default' => '#374151',
            ),

            // 紧凑布局调色板
            array(
                'id'      => 'compact_palette',
                'type'    => 'palette',
                'title'   => '紧凑布局调色板',
                'desc'    => '紧凑显示的小尺寸调色板，适合空间有限的场景',
                'layout'  => 'compact',
                'color_size' => 'small',
                'multiple' => true,
                'options' => array(
                    '#FF0000', '#FF3300', '#FF6600', '#FF9900', '#FFCC00', '#FFFF00',
                    '#CCFF00', '#99FF00', '#66FF00', '#33FF00', '#00FF00', '#00FF33',
                    '#00FF66', '#00FF99', '#00FFCC', '#00FFFF', '#00CCFF', '#0099FF',
                    '#0066FF', '#0033FF', '#0000FF', '#3300FF', '#6600FF', '#9900FF',
                    '#CC00FF', '#FF00FF', '#FF00CC', '#FF0099', '#FF0066', '#FF0033',
                ),
                'default' => array( '#FF0000', '#00FF00', '#0000FF' ),
            ),

        ),
    ) );

    // 创建 Sortable 可排序字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => 'Sortable 可排序字段测试',
        'desc'   => '展示现代化的可排序功能，支持拖拽排序、嵌套字段、多种布局模式等高级特性',
        'icon'   => 'dashicons-sort',
        'fields' => array(

            // 基础垂直排序
            array(
                'id'     => 'basic_sortable',
                'type'   => 'sortable',
                'title'  => '基础垂直排序',
                'desc'   => '简单的垂直排序列表，支持拖拽重新排序',
                'fields' => array(
                    array(
                        'id'    => 'feature_1',
                        'type'  => 'text',
                        'title' => '功能特性 1',
                        'desc'  => '描述第一个功能特性',
                    ),
                    array(
                        'id'    => 'feature_2',
                        'type'  => 'text',
                        'title' => '功能特性 2',
                        'desc'  => '描述第二个功能特性',
                    ),
                    array(
                        'id'    => 'feature_3',
                        'type'  => 'text',
                        'title' => '功能特性 3',
                        'desc'  => '描述第三个功能特性',
                    ),
                ),
                'default' => array(
                    'feature_1' => '响应式设计',
                    'feature_2' => '现代化界面',
                    'feature_3' => '高性能优化',
                ),
            ),

            // 水平布局排序
            array(
                'id'     => 'horizontal_sortable',
                'type'   => 'sortable',
                'title'  => '水平布局排序',
                'desc'   => '水平排列的可排序项目，适合标签或按钮排序',
                'layout' => 'horizontal',
                'fields' => array(
                    array(
                        'id'    => 'nav_home',
                        'type'  => 'text',
                        'title' => '首页',
                        'placeholder' => '首页链接文本',
                    ),
                    array(
                        'id'    => 'nav_about',
                        'type'  => 'text',
                        'title' => '关于',
                        'placeholder' => '关于页面文本',
                    ),
                    array(
                        'id'    => 'nav_services',
                        'type'  => 'text',
                        'title' => '服务',
                        'placeholder' => '服务页面文本',
                    ),
                    array(
                        'id'    => 'nav_contact',
                        'type'  => 'text',
                        'title' => '联系',
                        'placeholder' => '联系页面文本',
                    ),
                ),
                'default' => array(
                    'nav_home'     => '首页',
                    'nav_about'    => '关于我们',
                    'nav_services' => '我们的服务',
                    'nav_contact'  => '联系我们',
                ),
            ),

            // 网格布局排序
            array(
                'id'           => 'grid_sortable',
                'type'         => 'sortable',
                'title'        => '网格布局排序',
                'desc'         => '网格布局的可排序项目，适合卡片或模块排序',
                'layout'       => 'grid',
                'grid_columns' => 3,
                'fields'       => array(
                    array(
                        'id'    => 'card_1',
                        'type'  => 'fieldset',
                        'title' => '卡片 1',
                        'fields' => array(
                            array(
                                'id'    => 'title',
                                'type'  => 'text',
                                'title' => '标题',
                            ),
                            array(
                                'id'    => 'content',
                                'type'  => 'textarea',
                                'title' => '内容',
                                'rows'  => 3,
                            ),
                        ),
                    ),
                    array(
                        'id'    => 'card_2',
                        'type'  => 'fieldset',
                        'title' => '卡片 2',
                        'fields' => array(
                            array(
                                'id'    => 'title',
                                'type'  => 'text',
                                'title' => '标题',
                            ),
                            array(
                                'id'    => 'content',
                                'type'  => 'textarea',
                                'title' => '内容',
                                'rows'  => 3,
                            ),
                        ),
                    ),
                    array(
                        'id'    => 'card_3',
                        'type'  => 'fieldset',
                        'title' => '卡片 3',
                        'fields' => array(
                            array(
                                'id'    => 'title',
                                'type'  => 'text',
                                'title' => '标题',
                            ),
                            array(
                                'id'    => 'content',
                                'type'  => 'textarea',
                                'title' => '内容',
                                'rows'  => 3,
                            ),
                        ),
                    ),
                ),
                'default' => array(
                    'card_1' => array(
                        'title'   => '设计服务',
                        'content' => '提供专业的UI/UX设计服务',
                    ),
                    'card_2' => array(
                        'title'   => '开发服务',
                        'content' => '高质量的前端和后端开发',
                    ),
                    'card_3' => array(
                        'title'   => '维护服务',
                        'content' => '持续的技术支持和维护',
                    ),
                ),
            ),

            // 可添加删除的排序
            array(
                'id'              => 'dynamic_sortable',
                'type'            => 'sortable',
                'title'           => '动态排序列表',
                'desc'            => '支持添加、删除和排序的动态列表',
                'addable'         => true,
                'removable'       => true,
                'min_items'       => 1,
                'max_items'       => 10,
                'add_button_text' => '添加新项目',
                'fields'          => array(
                    array(
                        'id'    => 'dynamic_item',
                        'type'  => 'fieldset',
                        'title' => '动态项目',
                        'fields' => array(
                            array(
                                'id'          => 'item_title',
                                'type'        => 'text',
                                'title'       => '项目标题',
                                'placeholder' => '请输入项目标题',
                            ),
                            array(
                                'id'      => 'item_type',
                                'type'    => 'select',
                                'title'   => '项目类型',
                                'options' => array(
                                    'text'  => '文本内容',
                                    'image' => '图片内容',
                                    'video' => '视频内容',
                                    'link'  => '链接内容',
                                ),
                                'default' => 'text',
                            ),
                            array(
                                'id'          => 'item_content',
                                'type'        => 'textarea',
                                'title'       => '项目内容',
                                'placeholder' => '请输入项目内容',
                                'rows'        => 3,
                            ),
                        ),
                    ),
                ),
                'default' => array(
                    'dynamic_item' => array(
                        'item_title'   => '示例项目',
                        'item_type'    => 'text',
                        'item_content' => '这是一个示例项目的内容描述。',
                    ),
                ),
            ),

            // 可折叠的排序
            array(
                'id'          => 'collapsible_sortable',
                'type'        => 'sortable',
                'title'       => '可折叠排序',
                'desc'        => '支持折叠展开的排序列表，节省空间',
                'collapsible' => true,
                'show_preview' => true,
                'fields'      => array(
                    array(
                        'id'    => 'section_1',
                        'type'  => 'fieldset',
                        'title' => '页面头部',
                        'fields' => array(
                            array(
                                'id'    => 'logo',
                                'type'  => 'text',
                                'title' => 'Logo文本',
                            ),
                            array(
                                'id'    => 'tagline',
                                'type'  => 'text',
                                'title' => '标语',
                            ),
                            array(
                                'id'      => 'header_style',
                                'type'    => 'select',
                                'title'   => '头部样式',
                                'options' => array(
                                    'classic' => '经典样式',
                                    'modern'  => '现代样式',
                                    'minimal' => '简约样式',
                                ),
                            ),
                        ),
                    ),
                    array(
                        'id'    => 'section_2',
                        'type'  => 'fieldset',
                        'title' => '主要内容',
                        'fields' => array(
                            array(
                                'id'    => 'main_title',
                                'type'  => 'text',
                                'title' => '主标题',
                            ),
                            array(
                                'id'    => 'main_content',
                                'type'  => 'textarea',
                                'title' => '主要内容',
                                'rows'  => 5,
                            ),
                        ),
                    ),
                    array(
                        'id'    => 'section_3',
                        'type'  => 'fieldset',
                        'title' => '页面底部',
                        'fields' => array(
                            array(
                                'id'    => 'copyright',
                                'type'  => 'text',
                                'title' => '版权信息',
                            ),
                            array(
                                'id'    => 'footer_links',
                                'type'  => 'textarea',
                                'title' => '底部链接',
                                'rows'  => 3,
                            ),
                        ),
                    ),
                ),
                'default' => array(
                    'section_1' => array(
                        'logo'         => 'My Website',
                        'tagline'      => '专业的网站解决方案',
                        'header_style' => 'modern',
                    ),
                    'section_2' => array(
                        'main_title'   => '欢迎来到我们的网站',
                        'main_content' => '我们提供高质量的产品和服务，致力于为客户创造价值。',
                    ),
                    'section_3' => array(
                        'copyright'    => '© 2024 My Website. All rights reserved.',
                        'footer_links' => '隐私政策 | 服务条款 | 联系我们',
                    ),
                ),
            ),

        ),
    ) );


    // 创建现代化颜色选择器字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => '现代化颜色选择器测试',
        'desc'   => '展示现代化颜色选择器字段的强大功能，支持多种颜色格式、透明度、调色板、历史记录、取色器等高级功能',
        'icon'   => 'dashicons-art',
        'fields' => array(

            // 基本颜色选择器
            array(
                'id'      => 'basic_color',
                'type'    => 'color',
                'title'   => '基本颜色选择器',
                'desc'    => '最简单的颜色选择器，支持HEX格式',
                'default' => '#3B82F6',
            ),

            // 支持透明度的颜色选择器
            array(
                'id'      => 'rgba_color',
                'type'    => 'color',
                'title'   => 'RGBA颜色选择器',
                'desc'    => '支持透明度调节的颜色选择器',
                'default' => 'rgba(59, 130, 246, 0.8)',
                'format'  => 'rgba',
                'alpha'   => true,
            ),

            // HSL格式颜色选择器
            array(
                'id'      => 'hsl_color',
                'type'    => 'color',
                'title'   => 'HSL颜色选择器',
                'desc'    => '使用HSL格式的颜色选择器',
                'default' => 'hsl(217, 91%, 60%)',
                'format'  => 'hsl',
            ),

            // 带调色板的颜色选择器
            array(
                'id'      => 'palette_color',
                'type'    => 'color',
                'title'   => '自定义调色板',
                'desc'    => '包含自定义调色板的颜色选择器',
                'default' => '#10B981',
                'palette' => array(
                    '#EF4444', '#F97316', '#F59E0B', '#EAB308', '#84CC16',
                    '#22C55E', '#10B981', '#14B8A6', '#06B6D4', '#0EA5E9',
                    '#3B82F6', '#6366F1', '#8B5CF6', '#A855F7', '#D946EF',
                    '#EC4899', '#F43F5E', '#64748B', '#6B7280', '#374151'
                ),
            ),

            // 启用所有功能的颜色选择器
            array(
                'id'             => 'advanced_color',
                'type'           => 'color',
                'title'          => '高级颜色选择器',
                'desc'           => '启用所有高级功能：透明度、历史记录、对比度检查、取色器等',
                'default'        => '#8B5CF6',
                'format'         => 'rgba',
                'alpha'          => true,
                'history'        => true,
                'contrast_check' => true,
                'eyedropper'     => true,
                'show_toolbar'   => true,
                'show_status'    => true,
                'height'         => '400px',
            ),

        ),
    ) );

    // 创建 Switch 开关字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => 'Switch 开关字段测试',
        'desc'   => '展示现代化开关字段的强大功能，支持多种尺寸、颜色主题、动画效果和无障碍访问特性',
        'icon'   => 'dashicons-controls-play',
        'fields' => array(

            // 基础开关
            array(
                'id'      => 'basic_switch',
                'type'    => 'switch',
                'title'   => '基础开关',
                'desc'    => '最简单的开关字段，默认中等尺寸和蓝色主题',
                'default' => false,
            ),

            // 默认开启的开关
            array(
                'id'      => 'default_on_switch',
                'type'    => 'switch',
                'title'   => '默认开启',
                'desc'    => '默认状态为开启的开关',
                'default' => true,
            ),

            // 带标签的开关
            array(
                'id'      => 'labeled_switch',
                'type'    => 'switch',
                'title'   => '带标签开关',
                'desc'    => '开关旁边显示说明标签',
                'label'   => '启用此功能',
                'default' => false,
            ),

            // 小尺寸开关
            array(
                'id'      => 'small_switch',
                'type'    => 'switch',
                'title'   => '小尺寸开关',
                'desc'    => '紧凑的小尺寸开关，适合密集布局',
                'size'    => 'small',
                'label'   => '小开关',
                'default' => false,
            ),

            // 大尺寸开关
            array(
                'id'      => 'large_switch',
                'type'    => 'switch',
                'title'   => '大尺寸开关',
                'desc'    => '醒目的大尺寸开关，适合重要设置',
                'size'    => 'large',
                'label'   => '大开关',
                'default' => true,
            ),

            // 绿色主题开关
            array(
                'id'      => 'green_switch',
                'type'    => 'switch',
                'title'   => '绿色主题开关',
                'desc'    => '使用绿色主题的开关，通常用于表示成功或启用状态',
                'color'   => 'green',
                'label'   => '成功状态',
                'default' => true,
            ),

            // 红色主题开关
            array(
                'id'      => 'red_switch',
                'type'    => 'switch',
                'title'   => '红色主题开关',
                'desc'    => '使用红色主题的开关，通常用于表示警告或危险操作',
                'color'   => 'red',
                'label'   => '危险操作',
                'default' => false,
            ),

            // 紫色主题开关
            array(
                'id'      => 'purple_switch',
                'type'    => 'switch',
                'title'   => '紫色主题开关',
                'desc'    => '使用紫色主题的开关，现代化的配色方案',
                'color'   => 'purple',
                'size'    => 'large',
                'label'   => '高级功能',
                'default' => false,
            ),

            // 黄色主题开关
            array(
                'id'      => 'yellow_switch',
                'type'    => 'switch',
                'title'   => '黄色主题开关',
                'desc'    => '使用黄色主题的开关，通常用于表示警告或注意事项',
                'color'   => 'yellow',
                'label'   => '警告功能',
                'default' => false,
            ),

            // 粉色主题开关
            array(
                'id'      => 'pink_switch',
                'type'    => 'switch',
                'title'   => '粉色主题开关',
                'desc'    => '使用粉色主题的开关，适合个性化设置',
                'color'   => 'pink',
                'size'    => 'small',
                'label'   => '个性化',
                'default' => true,
            ),

            // 灰色主题开关
            array(
                'id'      => 'gray_switch',
                'type'    => 'switch',
                'title'   => '灰色主题开关',
                'desc'    => '使用灰色主题的开关，中性的配色方案',
                'color'   => 'gray',
                'label'   => '中性选项',
                'default' => false,
            ),

            // 带图标的开关
            array(
                'id'         => 'icon_switch',
                'type'       => 'switch',
                'title'      => '图标开关',
                'desc'       => '开关滑块内显示X和✓图标',
                'show_icons' => true,
                'size'       => 'medium',
                'color'      => 'blue',
                'default'    => false,
            ),

            // Short Toggle 样式开关
            array(
                'id'      => 'short_toggle',
                'type'    => 'switch',
                'title'   => 'Short Toggle 开关',
                'desc'    => '简洁的短开关样式，更紧凑的设计',
                'style'   => 'short',
                'color'   => 'blue',
                'default' => false,
            ),

            // Short Toggle 不同颜色
            array(
                'id'      => 'short_toggle_green',
                'type'    => 'switch',
                'title'   => 'Short Toggle - 绿色',
                'desc'    => '绿色主题的短开关',
                'style'   => 'short',
                'color'   => 'green',
                'default' => true,
            ),

            // 禁用状态开关
            array(
                'id'       => 'disabled_switch',
                'type'     => 'switch',
                'title'    => '禁用状态开关',
                'desc'     => '演示禁用状态的开关，用户无法操作',
                'disabled' => true,
                'label'    => '已禁用',
                'default'  => true,
            ),

            // 加载状态开关
            array(
                'id'      => 'loading_switch',
                'type'    => 'switch',
                'title'   => '加载状态开关',
                'desc'    => '演示加载状态的开关，显示旋转图标',
                'loading' => true,
                'label'   => '加载中...',
                'default' => false,
            ),

            // 组合示例：不同尺寸对比
            array(
                'id'      => 'size_comparison_small',
                'type'    => 'switch',
                'title'   => '尺寸对比 - 小',
                'desc'    => '小尺寸开关示例',
                'size'    => 'small',
                'color'   => 'green',
                'label'   => '小',
                'default' => true,
            ),

            array(
                'id'      => 'size_comparison_medium',
                'type'    => 'switch',
                'title'   => '尺寸对比 - 中',
                'desc'    => '中等尺寸开关示例（默认）',
                'size'    => 'medium',
                'color'   => 'green',
                'label'   => '中',
                'default' => true,
            ),

            array(
                'id'      => 'size_comparison_large',
                'type'    => 'switch',
                'title'   => '尺寸对比 - 大',
                'desc'    => '大尺寸开关示例',
                'size'    => 'large',
                'color'   => 'green',
                'label'   => '大',
                'default' => true,
            ),

        ),
    ) );
   

// 创建图片库字段测试区块
XUN::createSection( 'xun_test_options', array(
    'title'  => '图片库字段测试',
    'desc'   => '展示现代化图片库管理功能，支持拖拽排序、批量操作、响应式设计',
    'icon'   => 'dashicons-format-gallery',
    'fields' => array(

        // 基础图片库
        array(
            'id'    => 'basic_gallery',
            'type'  => 'gallery',
            'title' => '基础图片库',
            'desc'  => '最简单的图片库配置，支持多图片上传和管理',
        ),

        // 限制数量的图片库
        array(
            'id'         => 'limited_gallery',
            'type'       => 'gallery',
            'title'      => '限制数量图片库',
            'desc'       => '最多只能上传5张图片的图片库',
            'max_files'  => 5,
            'min_files'  => 1,
        ),

        // 自定义网格布局
        array(
            'id'           => 'custom_grid_gallery',
            'type'         => 'gallery',
            'title'        => '自定义网格布局',
            'desc'         => '自定义响应式网格列数的图片库',
            'grid_columns' => array(
                'sm' => 1,
                'md' => 2,
                'lg' => 3,
                'xl' => 4
            ),
        ),

        // 禁用功能的图片库
        array(
            'id'             => 'simple_gallery',
            'type'           => 'gallery',
            'title'          => '简化功能图片库',
            'desc'           => '禁用排序和批量操作的简单图片库',
            'enable_sorting' => false,
            'enable_batch'   => false,
            'enable_preview' => false,
        ),

        // 自定义文本的图片库
        array(
            'id'              => 'custom_text_gallery',
            'type'            => 'gallery',
            'title'           => '自定义文本图片库',
            'desc'            => '使用自定义提示文本的图片库',
            'add_title'       => '选择图片',
            'edit_title'      => '管理图片',
            'clear_title'     => '清空所有',
            'upload_text'     => '拖拽图片到这里或点击上传',
            'upload_hint'     => '支持 JPG、PNG、WebP 格式，单张图片不超过2MB',
            'empty_text'      => '还没有添加任何图片，快来上传第一张吧！',
        ),

        // 大网格图片库
        array(
            'id'           => 'large_grid_gallery',
            'type'         => 'gallery',
            'title'        => '大网格图片库',
            'desc'         => '使用更大网格显示的图片库，适合展示高质量图片',
            'preview_size' => 'medium',
            'grid_columns' => array(
                'sm' => 1,
                'md' => 2,
                'lg' => 2,
                'xl' => 3
            ),
        ),

        // 产品图片库示例
        array(
            'id'         => 'product_gallery',
            'type'       => 'gallery',
            'title'      => '产品图片库',
            'desc'       => '模拟产品图片管理，限制最多10张图片',
            'max_files'  => 10,
            'min_files'  => 1,
            'add_title'  => '添加产品图片',
            'edit_title' => '编辑产品图片',
            'upload_text' => '上传产品图片',
            'upload_hint' => '建议上传高质量的产品图片，尺寸不小于800x600',
        ),

        // 作品集图片库
        array(
            'id'           => 'portfolio_gallery',
            'type'         => 'gallery',
            'title'        => '作品集图片库',
            'desc'         => '展示作品集的图片库，支持大图预览',
            'preview_size' => 'large',
            'grid_columns' => array(
                'sm' => 1,
                'md' => 1,
                'lg' => 2,
                'xl' => 2
            ),
            'enable_preview' => true,
        ),

    ),
) );
    // 创建数字输入字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => '数字输入字段测试',
        'desc'   => '展示功能强大的数字输入字段',
        'icon'   => 'dashicons-calculator',
        'fields' => array(

            // 基础数字输入
            array(
                'id'    => 'basic_number',
                'type'  => 'number',
                'title' => '基础数字输入',
                'desc'  => '最简单的数字输入字段',
                'default' => 10,
            ),

            // 带范围限制的数字输入
            array(
                'id'    => 'range_number',
                'type'  => 'number',
                'title' => '范围限制数字',
                'desc'  => '限制数值范围在1-100之间，步长为5',
                'min'   => 1,
                'max'   => 100,
                'step'  => 5,
                'default' => 50,
                'range_indicator' => true,
            ),

            // 小数数字输入
            array(
                'id'    => 'decimal_number',
                'type'  => 'number',
                'title' => '小数数字输入',
                'desc'  => '支持2位小数的数字输入',
                'step'  => 0.01,
                'precision' => 2,
                'default' => 3.14,
                'placeholder' => '请输入小数',
            ),

            // 带单位的数字输入
            array(
                'id'    => 'unit_number',
                'type'  => 'number',
                'title' => '带单位数字',
                'desc'  => '右侧显示单位的数字输入',
                'unit'  => 'px',
                'min'   => 0,
                'max'   => 1000,
                'default' => 16,
                'width' => 'medium',
            ),

            // 左侧单位数字输入
            array(
                'id'    => 'left_unit_number',
                'type'  => 'number',
                'title' => '左侧单位数字',
                'desc'  => '左侧显示单位的数字输入',
                'unit'  => 'rem',
                'unit_position' => 'left',
                'step'  => 0.1,
                'precision' => 1,
                'default' => 1.5,
                'width' => 'medium',
            ),

            // 货币模式
            array(
                'id'    => 'currency_number',
                'type'  => 'number',
                'title' => '货币输入',
                'desc'  => '货币模式的数字输入，支持千分位分隔符',
                'mode'  => 'currency',
                'currency_symbol' => '$',
                'currency_position' => 'left',
                'precision' => 2,
                'thousand_sep' => true,
                'min'   => 0,
                'default' => 1299.99,
                'width' => 'large',
            ),

            // 百分比模式
            array(
                'id'    => 'percentage_number',
                'type'  => 'number',
                'title' => '百分比输入',
                'desc'  => '百分比模式的数字输入',
                'mode'  => 'percentage',
                'min'   => 0,
                'max'   => 100,
                'precision' => 1,
                'default' => 85.5,
                'width' => 'small',
            ),

            // 无增减按钮
            array(
                'id'    => 'no_spinner_number',
                'type'  => 'number',
                'title' => '无增减按钮',
                'desc'  => '隐藏增减按钮的数字输入',
                'spinner' => false,
                'min'   => 0,
                'max'   => 999,
                'default' => 42,
            ),

            // 只读数字
            array(
                'id'    => 'readonly_number',
                'type'  => 'number',
                'title' => '只读数字',
                'desc'  => '只读状态的数字输入',
                'readonly' => true,
                'default' => 123,
                'unit'  => 'kg',
            ),

            // 必填数字
            array(
                'id'    => 'required_number',
                'type'  => 'number',
                'title' => '必填数字',
                'desc'  => '必填的数字输入字段',
                'required' => true,
                'min'   => 1,
                'placeholder' => '请输入数字（必填）',
            ),

            // 大数字输入
            array(
                'id'    => 'large_number',
                'type'  => 'number',
                'title' => '大数字输入',
                'desc'  => '支持大数字和千分位分隔符的输入',
                'thousand_sep' => true,
                'min'   => 0,
                'max'   => 999999999,
                'default' => 1234567,
                'width' => 'large',
                'format_display' => true,
            ),

            // 高级配置数字
            array(
                'id'    => 'advanced_number',
                'type'  => 'number',
                'title' => '高级配置数字',
                'desc'  => '包含所有高级功能的数字输入：范围指示器、自动选中、键盘导航等',
                'min'   => 0,
                'max'   => 1000,
                'step'  => 10,
                'precision' => 0,
                'unit'  => 'MB',
                'range_indicator' => true,
                'auto_select' => true,
                'keyboard_nav' => true,
                'validation' => true,
                'format_display' => true,
                'default' => 500,
                'width' => 'medium',
            ),

            // 温度输入示例
            array(
                'id'    => 'temperature_number',
                'type'  => 'number',
                'title' => '温度输入',
                'desc'  => '温度输入示例，支持负数',
                'min'   => -50,
                'max'   => 50,
                'step'  => 0.5,
                'precision' => 1,
                'unit'  => '°C',
                'default' => 23.5,
                'range_indicator' => true,
                'width' => 'small',
            ),

            // 全宽数字输入
            array(
                'id'    => 'full_width_number',
                'type'  => 'number',
                'title' => '全宽数字输入',
                'desc'  => '占满容器宽度的数字输入',
                'width' => 'full',
                'placeholder' => '全宽数字输入示例',
                'default' => 2024,
            ),

        ),
    ) );

    // 创建图标选择字段测试区块
    XUN::createSection( 'xun_test_options', array(
        'title'  => '图标选择字段测试',
        'desc'   => '展示Heroicons图标库集成功能，支持图标搜索、预览、多种尺寸和样式选择',
        'icon'   => 'dashicons-star-filled',
        'fields' => array(

            // 基础图标选择
            array(
                'id'    => 'basic_icon',
                'type'  => 'icon',
                'title' => '基础图标选择',
                'desc'  => '最简单的图标选择字段，默认24px outline样式',
                'default' => 'academic-cap',
                'placeholder' => '选择一个图标...',
            ),

            // 16px图标选择
            array(
                'id'    => 'small_icon',
                'type'  => 'icon',
                'title' => '小尺寸图标 (16px)',
                'desc'  => '16像素尺寸的图标选择，适用于小图标场景',
                'size'  => '16',
                'style' => 'solid',
                'default' => 'heart',
                'show_preview' => true,
            ),

            // 20px图标选择
            array(
                'id'    => 'medium_icon',
                'type'  => 'icon',
                'title' => '中等尺寸图标 (20px)',
                'desc'  => '20像素尺寸的图标选择，平衡了清晰度和空间占用',
                'size'  => '20',
                'style' => 'solid',
                'default' => 'star',
                'show_preview' => true,
            ),

            // 24px solid图标选择
            array(
                'id'    => 'large_solid_icon',
                'type'  => 'icon',
                'title' => '大尺寸实心图标 (24px)',
                'desc'  => '24像素实心样式图标，视觉效果更突出',
                'size'  => '24',
                'style' => 'solid',
                'default' => 'shield-check',
                'show_preview' => true,
                'show_search' => true,
            ),

            // 导航图标
            array(
                'id'    => 'nav_icon',
                'type'  => 'icon',
                'title' => '导航图标',
                'desc'  => '用于导航菜单的图标选择',
                'size'  => '24',
                'style' => 'outline',
                'default' => 'home',
                'placeholder' => '选择导航图标...',
            ),

            // 功能图标
            array(
                'id'    => 'feature_icon',
                'type'  => 'icon',
                'title' => '功能图标',
                'desc'  => '用于功能展示的图标选择',
                'size'  => '24',
                'style' => 'outline',
                'default' => 'cog-6-tooth',
                'show_preview' => true,
            ),

            // 社交媒体图标
            array(
                'id'    => 'social_icon',
                'type'  => 'icon',
                'title' => '社交媒体图标',
                'desc'  => '用于社交媒体链接的图标选择',
                'size'  => '20',
                'style' => 'solid',
                'default' => 'share',
                'show_preview' => true,
            ),

            // 状态图标
            array(
                'id'    => 'status_icon',
                'type'  => 'icon',
                'title' => '状态图标',
                'desc'  => '用于状态显示的图标选择',
                'size'  => '16',
                'style' => 'solid',
                'default' => 'check-circle',
                'show_preview' => true,
            ),

            // 无预览图标
            array(
                'id'    => 'no_preview_icon',
                'type'  => 'icon',
                'title' => '无预览图标',
                'desc'  => '不显示实时预览的图标选择字段',
                'size'  => '24',
                'style' => 'outline',
                'default' => 'eye-slash',
                'show_preview' => false,
                'show_search' => true,
            ),

            // 无搜索图标
            array(
                'id'    => 'no_search_icon',
                'type'  => 'icon',
                'title' => '无搜索图标',
                'desc'  => '不显示搜索功能的图标选择字段',
                'size'  => '24',
                'style' => 'outline',
                'default' => 'magnifying-glass-minus',
                'show_preview' => true,
                'show_search' => false,
            ),

        ),
    ) );


}

// 在init钩子中调用配置函数
add_action( 'init', 'xun_create_theme_options' );

/**
 * 获取主题选项值的便捷函数
 *
 * @param string $option_id 选项ID
 * @param mixed  $default   默认值
 * @return mixed 选项值
 */
function xun_get_theme_option( $option_id, $default = '' ) {
    if ( function_exists( 'xun_get_option' ) ) {
        return xun_get_option( 'xun_test_options', $option_id, $default );
    }
    return $default;
}

/**
 * 检查主题选项是否存在
 *
 * @param string $option_id 选项ID
 * @return bool 是否存在
 */
function xun_has_theme_option( $option_id ) {
    $value = xun_get_theme_option( $option_id );
    return ! empty( $value );
}

/**
 * 获取所有主题选项
 *
 * @return array 所有选项值
 */
function xun_get_all_theme_options() {
    if ( function_exists( 'xun_get_option' ) ) {
        return xun_get_option( 'xun_test_options' );
    }
    return array();
}
/**
 * 毛玻璃效果演示内容
 */
function xun_demo_glass_content( $args = array() ) {
    echo '<div class="relative p-8 text-center">';
    echo '<div class="absolute inset-0 bg-gradient-to-br from-purple-400 via-pink-500 to-red-500 opacity-20 rounded-lg"></div>';
    echo '<div class="relative z-10">';
    echo '<h3 class="text-2xl font-bold text-gray-900 mb-4">毛玻璃效果</h3>';
    echo '<p class="text-gray-700 mb-6">这是一个现代化的毛玻璃视觉效果演示，展示了半透明背景和模糊效果的结合。</p>';
    echo '<div class="grid grid-cols-1 md:grid-cols-3 gap-4">';

    $features = array(
        array('title' => '半透明背景', 'desc' => '使用backdrop-blur实现'),
        array('title' => '现代设计', 'desc' => '符合当前设计趋势'),
        array('title' => '视觉层次', 'desc' => '增强内容可读性'),
    );

    foreach ($features as $feature) {
        echo '<div class="bg-white bg-opacity-60 backdrop-blur-sm p-4 rounded-lg border border-white border-opacity-20">';
        echo '<h4 class="font-semibold text-gray-900 mb-2">' . esc_html($feature['title']) . '</h4>';
        echo '<p class="text-sm text-gray-700">' . esc_html($feature['desc']) . '</p>';
        echo '</div>';
    }

    echo '</div>';
    echo '</div>';
    echo '</div>';
}

/**
 * 异步内容演示（这里只是模拟，实际会通过JS处理）
 */
function xun_demo_async_content( $args = array() ) {
    // 这个函数实际上不会被调用，因为设置了async=true
    // 内容会通过JavaScript异步加载
    echo '<div class="text-center py-8">';
    echo '<p class="text-gray-500">此内容应该通过异步方式加载...</p>';
    echo '</div>';
}



/**
 * 错误处理演示
 */
function xun_demo_error_handling( $args = array() ) {
    // 故意抛出错误来演示错误处理
    throw new Exception( '这是一个演示错误，用于展示错误处理功能' );
}
